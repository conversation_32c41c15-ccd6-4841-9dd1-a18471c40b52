import 'dart:developer';

import 'package:newarc_platform/app_const.dart' as appConst;
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'package:newarc_platform/classes/immaginaProject.dart';
import 'package:newarc_platform/classes/property.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/common_utils.dart';
import 'package:newarc_platform/utils/downloadQuotationPDF.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/link.dart';
import 'package:newarc_platform/widget/UI/tab/popup_menu_button.dart';
import 'package:newarc_platform/widget/UI/tab/status_widget.dart';
import 'package:newarc_platform/widget/UI/tab/tag_widget.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;

class AgencyRequestSource extends DataTableSource {
  AgencyRequestSource({
    this.onAddressTap,
    required this.projects,
    this.isRequest,
  });

  bool? isRequest;
  List<ImmaginaProject> projects = [];

  Function(ImmaginaProject)? onAddressTap;

  @override
  DataRow? getRow(int index) {
    if (index < projects.length) {
      final project = projects[index];
      final projectStatus = project.requestStatus.toLowerCase();

      String showProjectStatus = projectStatus;

      if(projectStatus == CommonUtils.preventivazione){
        showProjectStatus = "Rich. in prev.";
      }else if(projectStatus == CommonUtils.daCompletare){
        showProjectStatus = "Rich. da completare";
      }else if(projectStatus == CommonUtils.inAnalisi){
        showProjectStatus = "Rich. in analisi";
      }else if(projectStatus == CommonUtils.bloccata){
        showProjectStatus = "Rich. bloccata";
      }


      bool isBlock = projectStatus == CommonUtils.bloccata;
      int daysPassed = calculateDaysPassed(project.projectCompletedDate);
      bool isCompleted = ((projectStatus == CommonUtils.completato)||(projectStatus == CommonUtils.confermata)) && daysPassed < 1;

      var address = isRequest == true
                        ? "${project.streetName ?? 'noStreetName'}, ${project.streetNumber ?? 'noStreetNum'}"
                        : "${project.streetName ?? 'noStreetName'}, ${project.streetNumber ?? 'noStreetNum'} ${project.housingUnit != null ? '-' : ''} ${project.housingUnit ?? ''}";
      if (project.addressInfo != null) {
        address = "${project.addressInfo!.streetName ?? 'noStreetName'} ${project.addressInfo!.streetNumber ?? 'noStreetNum'}, ${project.addressInfo!.city ?? 'noCity'} ${project.housingUnit != null ? '-' : ''} ${project.housingUnit ?? ''}";
      }

      bool isProfessionals = project.isForProfessionals ?? false;

      return DataRow(
        color: MaterialStateProperty.resolveWith<Color?>(
          (Set<MaterialState> states) {
            if (isCompleted) {
              return const Color(0xFF39C14F).withOpacity(0.1);
            }
            if (isBlock) {
              return const Color(0xffE82525).withOpacity(0.1);
            }
            return null;
          },
        ),
        cells: [
          ///Indirizzo
          DataCell(
            Stack(
              clipBehavior: Clip.none,
              children: [
                if (isProfessionals)
                  Positioned(
                    top: -18,
                    child: Row(
                      children: [
                        TagWidget(
                          text: "Professionals",
                          statusColor: Colors.black,
                        ),
                        SizedBox(width: 5),
                        isCompleted
                        ? TagWidget(
                            text: projectStatus.toCapitalized(),
                            statusColor: Color(0xff39C14F),
                          )
                        : SizedBox.shrink(),
                      ],
                    ),
                  ),
                if (isCompleted && !isProfessionals)
                  Positioned(
                    top: -18,
                    child: TagWidget(
                      text: projectStatus.toCapitalized(),
                      statusColor: Color(0xff39C14F),
                    ),
                  ),
                NarLinkWidget(
                  text: address,
                  textColor: Colors.black,
                  fontWeight: '700',
                  fontSize: 12,
                  overflow: TextOverflow.ellipsis,
                  onClick: () {
                    onAddressTap!(project);
                  },
                ),
              ],
            ),
          ),

          ///Project status
          DataCell(
            Row(
              children: [
                StatusWidget(
                  status: showProjectStatus,
                  statusColor: CommonUtils.getColor(projectStatus),
                ),
                SizedBox(
                  width: 10,
                ),
                if (isBlock)
                  PopupMenuOnHover(
                    note: project.blockedNotes,
                    reason: project.blockedSection ?? "",
                    isForProfessionals: project.isForProfessionals ?? false,
                  ),
              ],
            ),
          ),

          /// project -> Iniziato il
          DataCell(
            NarFormLabelWidget(
              label: project.statusChangedDate == null ? '' : DateFormat('dd/MM/yyyy').format(DateTime.fromMillisecondsSinceEpoch(project.statusChangedDate ?? 0)).toString(),
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
              overflow: TextOverflow.ellipsis,
            ),
          ),


          ///Fine progetto
          DataCell(
            NarFormLabelWidget(
              label: project.projectCompletedDate == null ? '' : DateFormat('dd/MM/yyyy').format(DateTime.fromMillisecondsSinceEpoch(project.projectCompletedDate ?? 0)).toString(),
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
              overflow: TextOverflow.ellipsis,
            ),
          ),


        ],
      );
    }

    return null;
  }

  int calculateDaysPassed(int? statusChangedDate) {
    if (statusChangedDate == null) return 0;
    final now = DateTime.now();
    final changedDate = DateTime.fromMillisecondsSinceEpoch(statusChangedDate);
    return now.difference(changedDate).inDays.clamp(0, 5);
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => projects.length;

  @override
  int get selectedRowCount => 0;
}
