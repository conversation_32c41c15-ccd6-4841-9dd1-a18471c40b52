import 'dart:async';
import 'dart:developer';
import 'dart:html' as html;
import 'dart:typed_data';
import 'package:file_picker/_internal/file_picker_web.dart';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image_compression_flutter/image_compression_flutter.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/link.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:video_player/video_player.dart';
import 'package:image/image.dart' as img;


/// *  textInputType - The type of information for which to optimize the text input control.
/// *  hintText - Text that suggests what sort of input the field accepts.
/// *  prefixIcon - Pass Icon if required
/// *  defaultText - If there is predefined value is there for a text field
/// *  focusNode - Currently focus node
/// *  obscureText - Is Password field?
/// *  controller - Text controller
/// *  functionValidate - Validation function(currently I have used to check empty field)
/// *  parametersValidate - Value to validate
/// *  actionKeyboard - Keyboard action eg. next, done, search, etc
/// *  onSubmitField - Done click action
/// *  onFieldTap - On focus on TextField
/// *  storageDirectory - Define the directory for storing the images. Default newarcHomes
/// *  removeExistingOnChange - choose if you wanted to replace existing image with the new. This feature is useful when you are dealing with Profile Picture where you have to replace the existing image with new image.
/// *  removeExistingMessage - A confirmation message before you remove the existing image. The option is valid if removeExistingOnChange is set to TRUE.

class NarFilePickerWidget extends StatefulWidget {
  // final String? label;
  // final Color? textColor;
  // String? fontWeight = '800';
  // final double? fontSize;
  // final TextAlign? textAlign;
  // final NarButtonWidget? openPopup;

  // Popup and images config
  final bool? allowMultiple;
  final List<String>? allowedExtensions;
  final bool? removeButton;
  final String? removeButtonText;
  final BuildContext? pageContext;
  final String? actionButtonPosition;
  final String? uploadButtonPosition; //top, bottom
  final double? bottomActionSpace;

  final double? containerHeight;
  final double? containerWidth;
  final double? containerMarginLeft;
  final double? containerBorderRadius;

  final num? filesToDisplayInList;
  final String? showMoreButtonText;
  final String? displayFormat;
  final void Function()? onTapOfImage;
  // Button config

  final String? text;
  final Color? textColor;
  final Color? color;
  final Color? splashColor;
  final Color? hoverColor;
  final double? borderRadius;
  final double? minWidth;
  final Color? borderSideColor;
  final TextStyle? style;
  final Widget? leadingIcon;
  final Widget? trailingIcon;
  final double? fontSize;
  final String? fontWeight;
  final List? allFiles;
  final String? storageDirectory;
  final bool? removeExistingOnChange;
  final String? removeExistingMessage;

  final bool? isDownloadable;
  final bool? hasPreview;
  

  final BoxDecoration? imageContainerBoxDecoration;
  final EdgeInsets? imageContainerPadding;
  final String? imageContainerUploadButtonAlignment;
  final String? uploadPopupText;
  final Function? onUploadCompleted;
  final List<String>? progressMessage;
  final bool? showTitle;
  final Widget? thumbnailIcon;
  final bool? displayButtonAsLink;
  final int? compressionQuality;
  final bool notAccent;
  
  final Color? removeButtonTextColor;
  final double? height;
  final double? buttonWidth;
  final Color? buttonTextColor;

  //---for image resize pass isImageResize == true and resizeImageSize = [[width,height,name]] also add compressionQuality
  final bool? isResizeImage;
  final List? resizeImageSize;
  // final List<Widget>? displayInlineWidget;

  NarFilePickerWidget(
      {this.allowMultiple = true,
        this.allowedExtensions,
      this.removeButton,
      this.actionButtonPosition = 'top',
      this.uploadButtonPosition = 'front',
      this.removeButtonText,
      this.containerHeight = 100,
      this.containerWidth = 100,
      this.filesToDisplayInList = 0,
      this.showMoreButtonText,
      this.displayFormat = 'row',
      this.text,
      this.textColor,
      this.color,
      this.splashColor,
      this.borderRadius,
      this.containerBorderRadius = 10,
      this.minWidth,
      this.borderSideColor,
      this.style,
      this.leadingIcon,
      this.trailingIcon,
      this.hoverColor,
      this.fontSize = 14,
      this.fontWeight = '800',
      this.allFiles,
      this.pageContext,
      this.storageDirectory = 'newarcHomes/',
      this.removeExistingOnChange = false,
      this.removeExistingMessage = '',
      this.imageContainerBoxDecoration =
          const BoxDecoration(color: Colors.white),
      this.imageContainerPadding = const EdgeInsets.all(0),
      this.imageContainerUploadButtonAlignment = 'start',
      this.isDownloadable = false,
      this.hasPreview = true,
      this.onUploadCompleted,
      this.progressMessage = const [''],
      this.uploadPopupText = 'Carica immagini',
      this.showTitle = true,
      this.thumbnailIcon,
      this.displayButtonAsLink = false,
      this.compressionQuality = 70,
      this.notAccent = false,
      this.removeButtonTextColor = const Color.fromRGBO(179, 0, 0, 1),
      this.height = 35,
      this.buttonWidth,
      this.buttonTextColor = Colors.white,
        this.isResizeImage = false,
        this.resizeImageSize,
      this.bottomActionSpace = 2,
      this.containerMarginLeft = 5, this.onTapOfImage,
      // this.displayInlineWidget
      });

  @override
  _NarFilePickerWidgetState createState() => _NarFilePickerWidgetState();
}

class _NarFilePickerWidgetState extends State<NarFilePickerWidget> {
  // FilePickerResult picker = ImagePicker();
  num hiddenImages = 0;
  num counter = 0;
  double? imageBorderRadius;
  List<String> preloadFiles = [];

  List<String> documentExts = ['pdf'];
  List<String> videoExts = ['mov', 'mp4'];
  List<String> imageExts = ['jpg', 'jpeg', 'png'];
  List<String> allowedExts = ['pdf', 'jpg', 'jpeg', 'png', 'dwg', 'mov', 'mp4','heic'];

  String uploadingProgressMessage = '';
  double bottomPaddingToError = 12;

  late VideoPlayerController _controller;
  double sizeMultiplier = 0.7;
  bool isVideoFullScreen = false;

  List<String> formMessages = [''];

  @override
  void initState() {
    super.initState();
  }

  @protected
  void didUpdateWidget(NarFilePickerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  onButtonPressed(BuildContext context){

    /**
     * Replace the existing file if removeExistingOnChange is set to true
     * If there are exiting files then it will remove from remote first and then upload new
     */
    
    if (widget.removeExistingOnChange == true && widget.allFiles!.length > 0 ) {

      showDialog(
        context: context,
        builder: (BuildContext _bc1) {
          return StatefulBuilder(
            builder: (BuildContext _bc2, StateSetter setState) {
              return Center(
                child: BaseNewarcPopup(
                  formErrorMessage: formMessages,
                  buttonText: 'Sostituisci',
                  onPressed: () async {
                    setState(() {
                      formMessages.clear();
                      formMessages.add('Deleting old file(s).');
                    });
                    

                    for (var i = 0; i < widget.allFiles!.length ; i++) {
                      String filename = widget.allFiles![i];

                      await deleteFile(widget.storageDirectory!, filename);
                      widget.allFiles!.remove(filename);
                      await widget.onUploadCompleted!();
                    }

                    setState(() {
                      formMessages.clear();
                    });
                    Navigator.pop(context);

                    return _showMyDialog(context);

                    return true;
                    
                  },
                  title: "Sostituisci file",
                  column: StatefulBuilder(
                    builder: (context, _setState) {
                      return Container(
                        width: 500,
                        margin: EdgeInsets.symmetric(vertical: 30),
                        child: Center(
                          child: ListView(
                            shrinkWrap: true,
                            children: [
                              NarFormLabelWidget(
                                label: "Vuoi davvero sostituire il file\ncon uno nuovo?",
                                fontSize: 18,
                                textAlign: TextAlign.center,
                                textColor: Color(0xff696969),
                                fontWeight: '600',
                              )
                            ],
                          ),
                        ),
                      );
                    }
                  ),
                )
              );
            });
        }
      );  
      // NarAlertDialog(context, 'Conferma', widget.removeExistingMessage, [
      //   GestureDetector(
      //     child: Container(
      //       padding: EdgeInsets.symmetric(horizontal: 14, vertical: 10),
      //       decoration: BoxDecoration(
      //         color: Theme.of(context).primaryColor,
      //         borderRadius: BorderRadius.circular(6),
      //       ),
      //       child: Text(
      //         "Yes",
      //         style: TextStyle(color: Colors.white),
      //       ),
      //     ),
      //     onTap: () async {
      //       Navigator.of(context).pop(true);

      //       setState(() {
      //         widget.progressMessage!.clear();
      //         widget.progressMessage!.add('Deleting old file(s).');
      //       });
            

      //       for (var i = 0; i < widget.allFiles!.length ; i++) {
      //         String filename = widget.allFiles![i];

      //         await deleteFile(widget.storageDirectory!, filename);
      //         widget.allFiles!.remove(filename);
      //         await widget.onUploadCompleted!();
      //       }

      //       setState(() {});

      //       return _showMyDialog(context);
      //     },
      //   ),
      //   GestureDetector(
      //     child: Container(
      //       padding: EdgeInsets.symmetric(horizontal: 14, vertical: 10),
      //       decoration: BoxDecoration(
      //         color: Colors.red,
      //         borderRadius: BorderRadius.circular(6),
      //       ),
      //       child: Text(
      //         "Annulla",
      //         style: TextStyle(color: Colors.white),
      //       ),
      //     ),
      //     onTap: () {
      //       Navigator.of(context).pop(true);
      //     },
      //   )
      // ]);
      // }
    } else {
      return _showMyDialog(context);
    }
  }

  Widget openDialogButton(context) {
    return widget.displayButtonAsLink!
    ? NarLinkWidget(
      text: widget.text ?? '',
      textDecoration: TextDecoration.underline,
      fontSize: widget.fontSize,
      fontWeight: widget.fontWeight,
      onClick: (){
        onButtonPressed(context);
      },
    )
    : BaseNewarcButton(
        buttonText: widget.text ?? '',
        notAccent: widget.notAccent,
        height: widget.height,
        width: widget.buttonWidth,
        color: widget.splashColor,
        textColor: widget.buttonTextColor,
        fontWeight: widget.fontWeight,
        onPressed: () {
          onButtonPressed(context);
        });
  }

  Widget? displayImages = NarFormLabelWidget(
    label: 'Nessuna immagine selezionata',
    fontWeight: '800',
  );

  _showMyDialog(BuildContext context) {
    return showDialog(
      context: context,
      barrierDismissible: false, // user must tap button!

      // barrierDismissible: ,
      builder: (_) {
        // builder: (context, setState) {

        return Center(
          child: BaseNewarcPopup(
              title: widget.uploadPopupText,
              noButton: true,
              column: StatefulBuilder(
                  builder: (BuildContext context, StateSetter setState) {
                return SingleChildScrollView(
                  child: Container(
                    height: 300,
                    width: 500,
                    child: Column(
                      children: [
                        Expanded(
                          flex: 40,
                          child: MouseRegion(
                            cursor: SystemMouseCursors.click,
                            child: GestureDetector(
                              onTap: () async {
                                await getImageFromGallery(context);
                                if (mounted) {
                                  setState(() {});
                                }
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                    color: Color.fromRGBO(240, 240, 240, 1),
                                    //shape: BoxShape.circle,
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(10))),
                                width: 400,
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.upload_file,
                                      size: 60,
                                      color: Color.fromRGBO(128, 128, 128, 1),
                                    ),
                                    SizedBox(
                                      height: 30,
                                    ),
                                    NarFormLabelWidget(
                                      label: "Clicca per caricare",
                                      fontWeight: '700',
                                      fontSize: 18,
                                      textColor: Color.fromRGBO(128, 128, 128, 1),
                                      textAlign: TextAlign.center,
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 30,
                        ),
                        widget.filesToDisplayInList == 0
                            ? Container()
                            : Expanded(
                                flex: 60,
                                child: displayImages!,
                              )
                      ],
                    ),
                  ),
                );
              })),
        );
      },
    );
  }

  getImageFromGallery(BuildContext context) async {

    setState(() {
      widget.progressMessage!.clear();
      widget.progressMessage! .add('Waiting...');
      Navigator.of(context).pop(true);
    });
    
    FilePickerResult? result = await FilePickerWeb.platform.pickFiles(
      allowMultiple: widget.allowMultiple!,
      allowedExtensions: widget.allowedExtensions ?? allowedExts,
      type: FileType.custom,
      allowCompression: true,
      compressionQuality: widget.compressionQuality!,
    );

    setState(() {
      widget.progressMessage!.clear();
      widget.progressMessage! .add('Checking...');
      
    });

    
    
    if (result != null) {
      
      List<XFile> tmpfiles = [];
      List<XFile> tmpfilesForResize = [];

      // Close the popup
      
      
      result.files.map((f) {

        Uint8List? fileBytes = f.bytes;
        img.Image? image = img.decodeImage(fileBytes!);
        List split0 = f.name.split('.');
        
        String _tmpExt = '';
        if( split0.length > 1 ) _tmpExt = split0[split0.length - 1];

        String filename = f.name
            .toString()
            .replaceAll('.$_tmpExt', '')
            .replaceAll(RegExp(r'[^\w]+'), '_');
        Uint8List? compressedBytes;
        if (image != null) {
          // Compress the image with a lower quality (e.g., 10) to reduce the size further

          if (_tmpExt == 'jpg' || _tmpExt == 'jpeg'){
              compressedBytes = Uint8List.fromList(
                img.encodeJpg(image, quality: widget.compressionQuality!), // Experiment with quality values (10 is very low)
              );

          } else if (_tmpExt == 'png') {
            compressedBytes = Uint8List.fromList(
              img.encodePng(image, level: 1),
            );
          } else if (_tmpExt == 'bmp') {
            compressedBytes = Uint8List.fromList(
              img.encodeBmp(image),
            );
          }else{
            compressedBytes = fileBytes;
          }


          XFile tmpFile = XFile.fromData(compressedBytes,
              name: filename + '.' + _tmpExt, length: f.size);
          tmpfiles.add(tmpFile);


          // Resize logic
          if ((widget.isResizeImage ?? false) && ["jpg",'jpeg','png','bmp'].contains(_tmpExt)) {
            if (widget.resizeImageSize?.isNotEmpty ?? false) {
              for (final size in widget.resizeImageSize!) {
                final int fixedWidth = size[0];
                final String name = size[2];
                final double aspectRatio = image.height / image.width;
                final int calculatedHeight = (fixedWidth * aspectRatio).floor();

                img.Image resizedImage = image;
                if (image.width > fixedWidth || image.height > calculatedHeight) {
                  resizedImage = img.copyResize(
                    image,
                    width: fixedWidth,
                    height: calculatedHeight,
                    interpolation: img.Interpolation.average,
                  );
                }else{
                  resizedImage = image;
                }

                Uint8List resizedBytes = (_tmpExt == 'png')
                    ? Uint8List.fromList(img.encodePng(resizedImage, level: 1))
                    : Uint8List.fromList(img.encodeJpg(resizedImage, quality: widget.compressionQuality ?? 70));

                final resizedFile = XFile.fromData(
                  resizedBytes,
                  name: '${filename}_$name.$_tmpExt',
                  length: resizedBytes.length,
                );

                tmpfilesForResize.add(resizedFile);
              }
            }
          }

          return tmpFile;

        } else {
         
          XFile tmpFile = XFile.fromData(f.bytes!,
              name: filename + '.' + _tmpExt, length: f.size);
          tmpfiles.add(tmpFile);
          return tmpFile;
        }

        
      }).toList();


      int counter = 0;
      tmpfiles.map((_f) async {
        await uploadFile(widget.storageDirectory!, _f.name, _f)
            .then((uploadTask) {
          uploadTask!.snapshotEvents.listen((TaskSnapshot taskSnapshot) async {
            switch (taskSnapshot.state) {
              case TaskState.running:
                double _percentage = 100.0 *
                    (taskSnapshot.bytesTransferred / taskSnapshot.totalBytes);
                // if(mounted) {
                setState(() {
                  widget.progressMessage!.clear();
                  widget.progressMessage!
                      .add('Uploading ' + _percentage.toStringAsFixed(0) + '%');
                });
                // }
                break;
              case TaskState.paused:
                break;
              case TaskState.canceled:
                break;
              case TaskState.error:
                break;
              case TaskState.success:
                if (mounted) {
                  setState(() {
                    widget.allFiles!.insert(0, _f.name);
                  });
                }

                counter++;

                // if( counter == tmpfiles.length ) {
                // print('complted-->');
                await widget.onUploadCompleted!();
                setState(() {
                  widget.progressMessage!.clear();
                  widget.progressMessage!.add('');
                });

                // }


                break;
            }
          });
        });
      }).toList();


      if(widget.isResizeImage ?? false){
        tmpfilesForResize.map((_file){
          uploadFile(widget.storageDirectory!, _file.name, _file).whenComplete((){
          });
        }).toList();
      }
    } else {
      setState(() {
        widget.progressMessage!.clear();
        widget.progressMessage! .add('');
        
      });
      // User canceled the picker
    }

    // displayImages = Wrap(
    //     children: widget.allFiles!.map((image) {
    //   return fileContainer(image);
    // }).toList());
    updateCounters();
    // Navigator.of(context).pop(true);
  }


  updateCounters() {
    counter = 0;
    hiddenImages = widget.allFiles!.length - widget.filesToDisplayInList!;
  }

  Widget fileActionButtons(fileurl, filename) {
    return Container(
      width: widget.containerWidth,
      child: Row(
        mainAxisAlignment:
            widget.isDownloadable == true && widget.removeButton == true
                ? MainAxisAlignment.spaceBetween
                : MainAxisAlignment.center,
        // mainAxisSize: MainAxisSize.min,
        children: [
          widget.isDownloadable == true
              ? Expanded(
                  child: NarLinkWidget(
                    text: 'Download',
                    fontSize: widget.fontSize,
                    fontWeight: '800',
                    onClick: () {
                      downloadFile(fileurl);
                    },
                    textAlign: widget.isDownloadable == true &&
                            widget.removeButton == true
                        ? TextAlign.left
                        : TextAlign.center,
                  ),
                )
              : SizedBox(
                  width: 0,
                ),
          widget.removeButton == true
              ? Expanded(
                  child: NarLinkWidget(
                    text: widget.removeButtonText,
                    fontWeight: '800',
                    fontSize: widget.fontSize,
                    textAlign: widget.isDownloadable == true &&
                            widget.removeButton == true
                        ? TextAlign.right
                        : TextAlign.center,
                    textColor: widget.removeButtonTextColor,
                    onClick: () async {
                      return await showDialog(
                          context: context,
                          barrierDismissible: false,
                          builder: (BuildContext context) {
                            return StatefulBuilder(builder:
                                (BuildContext context, StateSetter setState) {
                              return Center(
                                child: BaseNewarcPopup(
                                  title: 'Conferma',
                                  buttonText: 'Procedi',
                                  onPressed: () async {
                                    if (mounted) {
                                      setState(() {
                                        widget.progressMessage!.clear();
                                        widget.progressMessage!
                                            .add('Cancellazione $filename');
                                      });
                                    }

                                    await deleteFile(
                                        widget.storageDirectory!, filename);
                                    widget.allFiles!.remove(filename);
                                    await widget.onUploadCompleted!();

                                    if (mounted) {
                                      setState(() {
                                        updateCounters();
                                        widget.progressMessage!.clear();
                                        widget.progressMessage!.add('');
                                      });
                                    }
                                    return true;
                                  },
                                  column: Container(
                                      width: 300,
                                      margin:
                                          EdgeInsets.symmetric(vertical: 30),
                                      child: Center(
                                          child: ListView(
                                              shrinkWrap: true,
                                              children: [
                                            NarFormLabelWidget(
                                                label:
                                                    'Sei sicuro di voler eliminare questo elemento?')
                                          ]))),
                                ),
                              );
                            });
                          });
                    },
                  ),
                )
              : SizedBox(
                  width: 0,
                ),
        ],
      ),
    );
  }

  viewDocument(
      BuildContext context, String filename, String fileurl, String ext) {
    if (imageExts.indexOf(ext) > -1) {
      viewImage(context, fileurl, filename);
    } else if (ext == 'pdf') {
      viewPdf(context, fileurl, filename);
    } else if (videoExts.indexOf(ext) > -1) {
      viewVideo(context, fileurl, filename);
    }
  }

  // bool imageLoading = false;
  bool loadingMedia = false;

  gotoFile(BuildContext context, fileIndex) async {
    setState(() {
      loadingMedia = true;
    });

    await printUrl(widget.storageDirectory, '', widget.allFiles![fileIndex])
        .then((_fileUrl) {
      if (isVideoPlaying) {
        isVideoPlaying = false;
        _controller.dispose();
      }

      Navigator.pop(context);
      String filename = widget.allFiles![fileIndex];

      setState(() {
        loadingMedia = false;
      });

      String ext = getFileExtension(filename);
      if (imageExts.indexOf(ext) > -1) {
        viewImage(context, _fileUrl, filename);
      } else if (ext == 'pdf') {
        viewPdf(context, _fileUrl, filename);
      } else if (videoExts.indexOf(ext) > -1) {
        viewVideo(context, _fileUrl, filename);
      }
    }).onError((error, stackTrace) {
      // print({widget.allFiles!, fileIndex, widget.allFiles![fileIndex], error, stackTrace});
    });
  }

  nextButton(BuildContext context, String filename) {
    return Container(
      height: 50,
      width: 50,
      // margin: EdgeInsets.only(left:10),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(25)),
      child: Center(
        child: IconButton(
            onPressed: () async {
              int currentFileIndex = widget.allFiles!.indexOf(filename);

              int gotoFileIndex = 0;
              if (currentFileIndex == widget.allFiles!.length - 1) {
                gotoFileIndex = 0;
              } else {
                gotoFileIndex = currentFileIndex + 1;
              }
              gotoFile(context, gotoFileIndex);
            },
            splashRadius: 10,
            icon: Icon(
              Icons.arrow_forward,
              size: 25,
              color: Colors.black,
            )),
      ),
    );
  }

  Widget previousButton(BuildContext context, String filename) {
    return Container(
      // padding: const EdgeInsets.all(8.0),
      height: 50,
      width: 50,
      // margin: EdgeInsets.only(right:10),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(25)),
      child: Center(
        child: IconButton(
            onPressed: () async {
              int currentFileIndex = widget.allFiles!.indexOf(filename);

              int gotoFileIndex = 0;
              if (currentFileIndex == 0) {
                gotoFileIndex = widget.allFiles!.length - 1;
              } else {
                gotoFileIndex = currentFileIndex - 1;
              }
              gotoFile(context, gotoFileIndex);
            },
            splashRadius: 10,
            icon: Icon(
              Icons.arrow_back,
              size: 25,
              color: Colors.black,
            )),
      ),
    );
  }

  Future<Size> _calculateImageDimension(BuildContext context, String url) {
    Completer<Size> completer = Completer();
    Image image = Image.network(url);
    image.image.resolve(ImageConfiguration()).addListener(
      ImageStreamListener((ImageInfo info, bool _) {
        completer.complete(Size(
          info.image.width.toDouble(),
          info.image.height.toDouble(),
        ));
      }),
    );
    return completer.future;
  }

  viewImage(BuildContext context, String fileurl, String filename) {

    return showDialog(
      context: context,
      barrierDismissible: true, // user must tap button!
      builder: (_) {
        // builder: (context, setState) {
        return FutureBuilder<Size>(
          future: _calculateImageDimension(context, fileurl),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.max,
                children: [
                  CircularProgressIndicator(color: Theme.of(context).primaryColor,),
                  SizedBox(height:5),
                  NarFormLabelWidget(label: 'Loading...', textColor: Colors.white,)
                ],
              )
            );
            } else if (snapshot.hasError) {
              return AlertDialog(
                title: NarFormLabelWidget(label: 'Error'),
                content: NarFormLabelWidget(label: 'Could not load image'),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: NarFormLabelWidget(label: 'Close'),
                  ),
                ],
              );
            } else {
              final size = snapshot.data!;

              double imageWidth = 0;
              double imageHeight = 0;
              double displayWidth = 0;
              double displayHeight = 0;

              double maxImageWidth = MediaQuery.of(context).size.width;
              double maxImageHeight = MediaQuery.of(context).size.height;
              
              imageWidth = size.width.toDouble();
              imageHeight = size.height.toDouble();
              double aspectRatio = imageWidth / imageHeight;

              displayWidth = imageWidth;
              displayHeight = imageHeight;

              if (displayWidth > maxImageWidth) {
                displayWidth = maxImageWidth;
                displayHeight = displayWidth / aspectRatio;
              }

              if (displayHeight > maxImageHeight) {
                displayHeight = maxImageHeight;
                displayWidth = displayHeight * aspectRatio;
              }
              
              return Center(
                child: Wrap(
                  children: [
                    Material(
                      color: Colors.transparent,
                      
                      child:
                      Center(
                        child: Stack(
                          children: [
                            Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Container(
                                    width: displayWidth,
                                    height: displayHeight,
                                    padding: const EdgeInsets.all(0),
                                    // decoration: BoxDecoration(
                                    //   borderRadius: BorderRadius.circular(15.0),
                                    // ),

                                    child: ListView(
                                      padding: EdgeInsets.all(0),
                                      
                                      shrinkWrap: true,
                                      children: [
                                        Card(
                                          
                                          color: const Color.fromRGBO(
                                              255, 255, 255, 1),
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(15.0),
                                          ),
                                          clipBehavior: Clip.hardEdge,
                                          child: Column(
                                            children: [
                                              Container(
                                                color: const Color.fromARGB(
                                                    255, 228, 228, 228),
                                                width: displayWidth,
                                                height: displayHeight,
                                                child: Image.network(
                                                  fileurl,
                                                  fit: BoxFit.cover,
                                                ),
                                              )
                                            ],
                                          ),
                                        ),
                                      ],
                                    )),
                                loadingMedia
                                    ? NarFormLabelWidget(label: "loading media")
                                    : SizedBox(height: 0)
                              ],
                            ),
                            Positioned(
                              child: Container(
                                color: Color.fromRGBO(255, 255, 255, 0.8),
                                child: Padding(
                                  padding: const EdgeInsets.all(5),
                                  child: NarFormLabelWidget(label: filename),
                                ),
                              ),
                              top: 10,
                              left: 10,
                            ),
                            Positioned(
                              top: 10,
                              right: 70,
                              child: Container(
                                height: 50,
                                width: 50,
                                margin: EdgeInsets.only(left: 10),
                                decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(25)),
                                child: Center(
                                  child: IconButton(
                                    onPressed: () {
                                      downloadFile(fileurl);
                                    },
                                    splashRadius: 20,
                                    icon: Icon(
                                      Icons.download,
                                      color: Colors.black,
                                    )
                                  ),
                                ),
                              ),
                            ),
                            Positioned(
                              top: 10,
                              right: 10,
                              child: Container(
                                height: 50,
                                width: 50,
                                margin: EdgeInsets.only(left: 10),
                                decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(25)),
                                child: Center(
                                  child: IconButton(
                                      onPressed: () {
                                        Navigator.pop(context);
                                      },
                                      splashRadius: 20,
                                      icon: Icon(
                                        Icons.close,
                                        color: Colors.black,
                                      )),
                                ),
                              ),
                            ),
                            Positioned(
                              left: 0,
                              top: displayHeight / 2,
                              child: widget.allFiles!.length > 1
                                  ? previousButton(context, filename)
                                  : SizedBox(height: 0),
                            ),
                            Positioned(
                              right: 0,
                              top: displayHeight / 2,
                              child: widget.allFiles!.length > 1
                                  ? nextButton(context, filename)
                                  : SizedBox(height: 0),
                            )
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }
          },
        );
    
      },
    );
  }

  viewPdf(BuildContext context, String fileurl, String filename) async {
    final GlobalKey<SfPdfViewerState> _pdfViewerKey = GlobalKey();

    return showDialog(
      context: context,
      barrierDismissible: true, // user must tap button!
      builder: (_) {
        // builder: (context, setState) {
        return Material(
          color: Colors.transparent,
          child: Center(
            child: Stack(
              // mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                    width: MediaQuery.sizeOf(context).width,
                    height: MediaQuery.sizeOf(context).height,
                    padding: const EdgeInsets.all(0),
                    // color: const Color.fromRGBO(255, 255, 255, 1),
                    child: Card(
                      color: const Color.fromRGBO(255, 255, 255, 1),
                      child: Column(
                        children: [
                          
                          Container(
                            width: MediaQuery.sizeOf(context).width-10,
                            height: MediaQuery.sizeOf(context).height-10,
                            child: SfPdfViewer.network(
                              onDocumentLoaded: (details) {
                                // print('document loaded');
                              },
                              onDocumentLoadFailed: (details) {
                                // print({
                                //   'load failed',
                                //   details.error,
                                //   details.description
                                // });
                              },
                              fileurl,
                              key: _pdfViewerKey,
                            ),
                          )
                        ],
                      ),
                    )),
                Positioned(
                  child: Container(
                    color: Color.fromRGBO(255, 255, 255, 0.8),
                    child: Padding(
                      padding: const EdgeInsets.all(5),
                      child: NarFormLabelWidget(label: filename),
                    ),
                  ),
                  top: 10,
                  left: 10,
                ),
                Positioned(
                  top: 10,
                  right: 70,
                  child: Container(
                    height: 50,
                    width: 50,
                    margin: EdgeInsets.only(left: 10),
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(25)),
                    child: Center(
                      child: IconButton(
                          onPressed: () {
                            downloadFile(fileurl);
                          },
                          splashRadius: 20,
                          icon: Icon(
                            Icons.download,
                            color: Colors.black,
                          )),
                    ),
                  ),
                ),
                Positioned(
                  top: 10,
                  right: 10,
                  child: Container(
                    height: 50,
                    width: 50,
                    margin: EdgeInsets.only(left: 10),
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(25)),
                    child: Center(
                      child: IconButton(
                          onPressed: () {
                            Navigator.pop(context);
                          },
                          splashRadius: 20,
                          icon: Icon(
                            Icons.close,
                            color: Colors.black,
                          )),
                    ),
                  ),
                ),
                Positioned(
                  left: 0,
                  top: 325,
                  child: widget.allFiles!.length > 1
                      ? previousButton(context, filename)
                      : SizedBox(height: 0),
                ),
                Positioned(
                  right: 0,
                  top: 325,
                  child: widget.allFiles!.length > 1
                      ? nextButton(context, filename)
                      : SizedBox(height: 0),
                )
              ],
            ),
          ),
        );
      },
    );
  }

  bool isVideoPlaying = false;

  viewVideo(BuildContext context, String fileurl, String filename) async {
    _controller = VideoPlayerController.networkUrl(
      Uri.parse(fileurl),
      // closedCaptionFile: _loadCaptions(),
      videoPlayerOptions: VideoPlayerOptions(mixWithOthers: true),
    );

    final double containerWidth = MediaQuery.sizeOf(context).width;
    final double containerHeight = MediaQuery.sizeOf(context).height;
    // print({'sm', _controller.value.size.height, _controller.value.size.width, _controller.value.size });

    _controller.addListener(() {
      setState(() {});
    });
    _controller.setLooping(false);
    _controller.play();
    _controller.initialize();
    isVideoPlaying = true;

    return showDialog(
      context: context,
      barrierDismissible: true,

      // user must tap button!
      builder: (_) {
        // builder: (context, setState) {
        return Material(
          color: Colors.transparent,
          child: StatefulBuilder(
              builder: (BuildContext _context, StateSetter setState) {
            return Center(
              child: Stack(
                // mainAxisAlignment: MainAxisAlignment.center,
                // crossAxisAlignment: CrossAxisAlignment.center,
                // mainAxisSize: MainAxisSize.max,
                children: [
                  // widget.allFiles!.length > 1
                  //   ? previousButton(context, filename)
                  //   : SizedBox(height:0),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                          width: containerWidth * sizeMultiplier,
                          height: containerHeight * sizeMultiplier + 10,
                          padding: const EdgeInsets.all(0),
                          // color: const Color.fromRGBO(255, 255, 255, 1),
                          child: Card(
                            color: const Color.fromRGBO(255, 255, 255, 1),
                            clipBehavior: Clip.hardEdge,
                            child: Column(
                              children: [
                                // isVideoFullScreen
                                //     ? SizedBox(height: 0)
                                //     : Row(
                                //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                //         crossAxisAlignment: CrossAxisAlignment.center,
                                //         mainAxisSize: MainAxisSize.max,
                                //         children: [
                                //           Padding(
                                //             padding: const EdgeInsets.only(left: 10.0),
                                //             child: NarFormLabelWidget(label: filename),
                                //           ),
                                //           IconButton(
                                //               onPressed: () {
                                //                 isVideoFullScreen = false;
                                //                 sizeMultiplier = 0.7;

                                //                 Navigator.pop(context);
                                //               },
                                //               splashRadius: 20,
                                //               icon: Icon(
                                //                 Icons.close,
                                //                 color: Colors.black,
                                //               ))
                                //         ],
                                //       ),
                                Container(
                                  width: containerWidth * sizeMultiplier,
                                  height: containerHeight * sizeMultiplier,
                                  child: AspectRatio(
                                    aspectRatio: _controller.value.aspectRatio,
                                    child: Stack(
                                      alignment: Alignment.bottomCenter,
                                      children: <Widget>[
                                        VideoPlayer(_controller),
                                        ClosedCaption(
                                            text:
                                                _controller.value.caption.text),
                                        ControlsOverlay(
                                            controller: _controller),
                                        VideoProgressIndicator(_controller,
                                            allowScrubbing: true),
                                        Positioned(
                                            bottom: 10,
                                            right: 10,
                                            child: GestureDetector(
                                              onTap: () {
                                                setState(() {
                                                  if (!isVideoFullScreen) {
                                                    sizeMultiplier = 1;
                                                    isVideoFullScreen = true;
                                                  } else {
                                                    sizeMultiplier = 0.70;
                                                    isVideoFullScreen = false;
                                                  }
                                                  // print({ 'action', sizeMultiplier,isVideoFullScreen  });
                                                });
                                              },
                                              child: Container(
                                                  padding: EdgeInsets.symmetric(
                                                      horizontal: 7,
                                                      vertical: 5),
                                                  decoration: BoxDecoration(
                                                      color: Colors.white,
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              6),
                                                      border: Border.all(
                                                          color: Colors.black,
                                                          width: 1)),
                                                  child: Icon(isVideoFullScreen
                                                      ? Icons.fullscreen_exit
                                                      : Icons.fullscreen)),
                                            )),
                                        Positioned(
                                          child: Container(
                                            color: Color.fromRGBO(
                                                255, 255, 255, 0.8),
                                            child: Padding(
                                              padding: const EdgeInsets.all(5),
                                              child: NarFormLabelWidget(
                                                  label: filename),
                                            ),
                                          ),
                                          top: 10,
                                          left: 10,
                                        ),
                                        Positioned(
                                          top: 10,
                                          right: 70,
                                          child: Container(
                                            height: 50,
                                            width: 50,
                                            margin: EdgeInsets.only(left: 10),
                                            decoration: BoxDecoration(
                                                color: Colors.white,
                                                borderRadius: BorderRadius.circular(25)),
                                            child: Center(
                                              child: IconButton(
                                                  onPressed: () {
                                                    downloadFile(fileurl);
                                                  },
                                                  splashRadius: 20,
                                                  icon: Icon(
                                                    Icons.download,
                                                    color: Colors.black,
                                                  )),
                                            ),
                                          ),
                                        ),
                                        Positioned(
                                          top: 10,
                                          right: 10,
                                          child: Container(
                                            height: 50,
                                            width: 50,
                                            margin: EdgeInsets.only(left: 10),
                                            decoration: BoxDecoration(
                                                color: Colors.white,
                                                borderRadius:
                                                    BorderRadius.circular(25)),
                                            child: Center(
                                              child: IconButton(
                                                  onPressed: () {
                                                    Navigator.pop(context);
                                                  },
                                                  splashRadius: 20,
                                                  icon: Icon(
                                                    Icons.close,
                                                    color: Colors.black,
                                                  )),
                                            ),
                                          ),
                                        ),
                                        Positioned(
                                          left: 0,
                                          top: (containerHeight *
                                                  sizeMultiplier) /
                                              2,
                                          child: widget.allFiles!.length > 1
                                              ? previousButton(
                                                  context, filename)
                                              : SizedBox(height: 0),
                                        ),
                                        Positioned(
                                          right: 0,
                                          top: (containerHeight *
                                                  sizeMultiplier) /
                                              2,
                                          child: widget.allFiles!.length > 1
                                              ? nextButton(context, filename)
                                              : SizedBox(height: 0),
                                        )
                                      ],
                                    ),
                                  ),
                                )
                              ],
                            ),
                          )),
                      loadingMedia
                          ? NarFormLabelWidget(label: "loading media")
                          : SizedBox(height: 0)
                    ],
                  ),
                  // widget.allFiles!.length > 1
                  //   ? nextButton(context, filename)
                  //   : SizedBox(height:0),
                ],
              ),
            );
          }),
        );
      },
    );
  }

  getFileExtension(String filename) {
    List<String> split0 = filename.split('.');
    String filetype = split0[split0.length - 1].toLowerCase();
    return filetype;
  }

  Future<Widget> fileContainer(BuildContext context, dynamic _filedata) async {
    // List<String> split0 = filename.split('.');
    String filename = '';
    String label = '';
    if( _filedata.runtimeType == String ) {
      filename = _filedata;
    } else if( _filedata is Map ) {
      filename = _filedata['filename'];
      label =  _filedata['label'];
    }
    String filetype = getFileExtension(filename);
    String extFile = '';
    bool isImage = false;
    bool isVideo = false;

    if (documentExts.indexOf(filetype) > -1) {
      extFile = filetype;
    } else if (imageExts.indexOf(filetype) > -1) {
      isImage = true;
    } else if (videoExts.indexOf(filetype) > -1) {
      isVideo = true;
      extFile = 'video';
    } else {
      extFile = 'file';
    }

    return await printUrl(widget.storageDirectory, '', filename)
        .then((fileurl) {
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          widget.showTitle!
          ? Column(
            children: [
              Container(
                width: widget.containerWidth,
                child: NarFormLabelWidget(
                    label: filename,
                    fontSize: 12,
                    fontWeight: 'bold',
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis),
              ),
              SizedBox(height: 5)
            ],
          )
          : SizedBox(height: 0),
          widget.actionButtonPosition == 'top'
              ? Column(
                  children: [
                    fileActionButtons(fileurl, filename),
                    SizedBox(
                      height: 5,
                    )
                  ],
                )
              : SizedBox(height: 0),
          MouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: () {
                if (widget.onTapOfImage != null) {
                  widget.onTapOfImage!();
                } else if (widget.hasPreview ?? true) {
                  viewDocument(context, filename, fileurl, filetype);
                }

              },
              child: widget.thumbnailIcon == null
              
              ? Container(
                  height: widget.containerHeight,
                  width: widget.containerWidth,
                  margin: EdgeInsets.only(left: widget.containerMarginLeft ?? 5),
                  padding: EdgeInsets.all(isImage ? 0 : (widget.containerHeight! < 51 ? 0 : 20) ),
                  clipBehavior: Clip.hardEdge,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.all(
                      Radius.circular(widget.containerBorderRadius!),
                    ),
                    border: Border.all(
                      width: 1,
                      color: Color(0xffdbdbdb),
                    ),
                  ),
                  child: isImage
                    ? Image.network(
                        fileurl,
                        fit: BoxFit.cover,
                      )
                    : Image.asset(
                        'assets/icons/' + extFile + '.png',
                        color: Color(0xff666666),
                        height: 10,
                        width: 10,
                      )
                  )
              : widget.thumbnailIcon
            ),
          ),
          
          if( label != '' ) Container(
            margin: EdgeInsets.only(top: 5, left: 5, bottom: 10),
            width: widget.containerWidth,
            child: NarFormLabelWidget(
                      label: label,
                      fontSize: 12,
                      fontWeight: '600', 
                      textAlign: TextAlign.left,
                      overflow: TextOverflow.ellipsis,
                      textColor: Color(0xff717171),
                    ),
          ),
              

          widget.actionButtonPosition == 'bottom'
              ? Column(
                  children: [
                    SizedBox(
                      height: widget.bottomActionSpace,
                    ),
                    fileActionButtons(fileurl, filename),
                  ],
                )
              : SizedBox(height: 0),
          // SizedBox(height: 10)
        ],
      );
    });
  }

  downloadFile(String url) async {
    List<String> split0 = url.split('/');
    List<String> split1 = split0[split0.length - 1].split('?');
    List<String> split2 = split1[0].split('%2F');
    String filename = split2[split2.length - 1];

    // print({'download', filename, url});

    html.AnchorElement anchorElement = new html.AnchorElement(href: url);

    anchorElement.download = filename;
    anchorElement.target = '_blank';
    anchorElement.click();
  }

  Widget displayInColumn(context) {
    return SingleChildScrollView(
      // scrollDirection: Axis.vertical,
      child: Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              openDialogButton(context),
            ],
          ),
          SizedBox(
            height: 20,
          ),

          filesInAction(context),
          // widget.filesToDisplayInList! > 0 && hiddenImages > 0
          //     ? NarLinkWidget(
          //         text: widget.showMoreButtonText! +
          //             ' ' +
          //             hiddenImages.toString(),
          //         fontWeight: '800',
          //         fontSize: 15,
          //         onClick: () {
          //           _showMyDialog(context);
          //         },
          //       )
          //     : SizedBox(
          //         height: 0,
          //       ),
        ],
      ),
    );
  }

  Widget displayInRow(context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          decoration: widget.imageContainerBoxDecoration,
          // width: double.infinity,
          padding: widget.imageContainerPadding,
          child: Wrap(
            crossAxisAlignment: WrapCrossAlignment.center,
            // mainAxisAlignment: MainAxisAlignment.center,
            // mainAxisSize: MainAxisSize.min,
            children: [
              widget.uploadButtonPosition == 'front'
                  ? Row(
                      children: [
                        openDialogButton(context),
                        SizedBox(width: 15)
                      ],
                    )
                  : SizedBox(height: 0),
              SizedBox(
                height: 15,
              ),
              filesInAction(context),
              widget.uploadButtonPosition == 'back'
                  ? Row(
                      mainAxisAlignment:
                          widget.imageContainerUploadButtonAlignment == 'end'
                              ? MainAxisAlignment.end
                              : MainAxisAlignment.start,
                      children: [
                        SizedBox(width: 15),
                        openDialogButton(context)
                      ],
                    )
                  : SizedBox(height: 0),
            ],
          ),
        ),
      ],
    );
  }

  /*
  * This Method is important when 
  * the button and the widget needs to 
  * display on different places 
  */
  // bool isWidgetSet = false;
  Widget displayInlineButton(context) {
    return Column(
      // mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        openDialogButton(context),
        widget.progressMessage![0] != ''
            ? Column(
                children: [
                  NarFormLabelWidget(
                    label: widget.progressMessage![0],
                    fontSize: 10,
                  ),
                  SizedBox(height: 10),
                ],
              )
            : SizedBox(height:0)
      ],
    );
  }

  displayWidget(BuildContext context) {
    return filesInAction(context);
  }


  Widget filesInAction(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Progress message
        widget.progressMessage![0] != ''
            ? Column(
                children: [
                  NarFormLabelWidget(label: widget.progressMessage![0]),
                  SizedBox(height: 10),
                ],
              )
            : SizedBox(height: 0),

        widget.allFiles!.length > 0
            ? Wrap(
              spacing: 5.0,
              crossAxisAlignment: WrapCrossAlignment.start,
              children: widget.allFiles!.map((filename) {
                      counter++;
                      if (widget.filesToDisplayInList!.toInt() > 0 &&
                          counter > widget.filesToDisplayInList!.toInt())
                        return SizedBox(
                          height: 0,
                        );

                      return FutureBuilder(
                          future: fileContainer(context, filename),
                          initialData: Container(
                              height: widget.containerHeight,
                              width: widget.containerWidth,
                              margin: EdgeInsets.only(right: 5),
                              clipBehavior: Clip.hardEdge,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.all(
                                  Radius.circular(widget.containerBorderRadius!),
                                ),
                                border: Border.all(
                                  width: 1,
                                  color: Color(0xffdbdbdb),
                                ),
                              ),
                              child: Center(
                                  child: NarFormLabelWidget(
                                label: 'Loading',
                                fontSize: 11,
                              ))),
                          builder: (BuildContext _context, AsyncSnapshot snapshot) {
                            if (snapshot.hasData) {
                              return snapshot.data!;
                            } else {
                              return Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: [

                                  widget.showTitle!
                                  ? Column(
                                    children: [
                                      Container(
                                        width: widget.containerWidth,
                                        child: NarFormLabelWidget(
                                            label: filename,
                                            fontSize: 12,
                                            fontWeight: 'bold',
                                            textAlign: TextAlign.center,
                                            overflow: TextOverflow.ellipsis),
                                      ),
                                      SizedBox(height: 5),
                                    ],
                                  )
                                  : Container(),
                                  Container(
                                      height: widget.containerHeight,
                                      width: widget.containerWidth,
                                      margin: EdgeInsets.only(right: 5),
                                      clipBehavior: Clip.hardEdge,
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.all(
                                          Radius.circular(
                                              widget.containerBorderRadius!),
                                        ),
                                        border: Border.all(
                                          width: 1,
                                          color: Color(0xffdbdbdb),
                                        ),
                                      ),
                                      child: Center(
                                          child: NarFormLabelWidget(
                                        label: 'Error loading file',
                                        fontSize: 11,
                                      ))),
                                ],
                              );
                            }
                          });
                      // return Image.file(File(image.path));
                    }).toList(),


            )
            : Container(),
        //: NarFormLabelWidget(label: 'No files found!'),
        widget.filesToDisplayInList! > 0 && hiddenImages > 0
            ? NarLinkWidget(
                text:
                    widget.showMoreButtonText! + ' ' + hiddenImages.toString(),
                fontWeight: '800',
                fontSize: 15,
                onClick: () {
                  _showMyDialog(context);
                },
              )
            : SizedBox(
                height: 0,
              ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.displayFormat == 'row') {
      return displayInRow(context);
    } else if (widget.displayFormat == 'column') {
      return displayInColumn(context);
    } else if (widget.displayFormat == 'inline-button') {
      // print({'build context'});
      return displayInlineButton(context);
    } else if (widget.displayFormat == 'inline-widget') {
      // print({'build context 2'});
      return displayWidget(context);
    } else {
      return displayInRow(context);
    }
    // return SizedBox(height:0);
  }
}

class ControlsOverlay extends StatefulWidget {
  final VideoPlayerController controller;
  const ControlsOverlay({Key? key, required this.controller}) : super(key: key);

  @override
  State<ControlsOverlay> createState() => _ControlsOverlayState();
}

class _ControlsOverlayState extends State<ControlsOverlay> {
  static const List<Duration> videoCaptionOffsets = <Duration>[
    Duration(seconds: -10),
    Duration(seconds: -3),
    Duration(seconds: -1, milliseconds: -500),
    Duration(milliseconds: -250),
    Duration.zero,
    Duration(milliseconds: 250),
    Duration(seconds: 1, milliseconds: 500),
    Duration(seconds: 3),
    Duration(seconds: 10),
  ];
  static const List<double> videoPlaybackRates = <double>[
    0.25,
    0.5,
    1.0,
    1.5,
    2.0,
    3.0,
    5.0,
    10.0,
  ];

  // final VideoPlayerController controller;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: <Widget>[
        AnimatedSwitcher(
          duration: const Duration(milliseconds: 50),
          reverseDuration: const Duration(milliseconds: 200),
          child: widget.controller.value.isPlaying
              ? const SizedBox.shrink()
              : const ColoredBox(
                  color: Colors.black26,
                  child: Center(
                    child: Icon(
                      Icons.play_arrow,
                      color: Colors.white,
                      size: 100.0,
                      semanticLabel: 'Play',
                    ),
                  ),
                ),
        ),
        GestureDetector(
          onTap: () {
            widget.controller.value.isPlaying
                ? widget.controller.pause()
                : widget.controller.play();
            setState(() {});
          },
        ),
        // Align(
        //   alignment: Alignment.topLeft,
        //   child: PopupMenuButton<Duration>(
        //     initialValue: widget.controller.value.captionOffset,
        //     tooltip: 'Caption Offset',
        //     onSelected: (Duration delay) {
        //       widget.controller.setCaptionOffset(delay);
        //     },
        //     itemBuilder: (BuildContext context) {
        //       return <PopupMenuItem<Duration>>[
        //         for (final Duration offsetDuration in videoCaptionOffsets)
        //           PopupMenuItem<Duration>(
        //             value: offsetDuration,
        //             child: NarFormLabelWidget ( label: '${offsetDuration.inMilliseconds}ms'),
        //           )
        //       ];
        //     },
        //     child: Padding(
        //       padding: const EdgeInsets.symmetric(
        //         vertical: 12,
        //         horizontal: 16,
        //       ),
        //       child: NarFormLabelWidget ( label: '${widget.controller.value.captionOffset.inMilliseconds}ms'),
        //     ),
        //   ),
        // ),
        // Align(
        //   alignment: Alignment.topRight,
        //   child: Row(
        //     mainAxisSize: MainAxisSize.min,
        //     crossAxisAlignment: CrossAxisAlignment.center,
        //     children: [
        //       // NarFormLabelWidget(label: 'Playback Speed'),
        //       // SizedBox(width: 10,),
        //       PopupMenuButton<double>(
        //         initialValue: widget.controller.value.playbackSpeed,
        //         tooltip: 'Playback speed',

        //         onSelected: (double speed) {
        //           widget.controller.setPlaybackSpeed(speed);
        //           setState(() {});
        //         },
        //         itemBuilder: (BuildContext context) {
        //           return <PopupMenuItem<double>>[
        //             for (final double speed in videoPlaybackRates)
        //               PopupMenuItem<double>(
        //                 value: speed,
        //                 child: NarFormLabelWidget(label: '${speed}x'),
        //               )
        //           ];
        //         },
        //         child: Padding(
        //           padding: const EdgeInsets.symmetric(
        //             vertical: 12,
        //             horizontal: 16,
        //           ),
        //           child: Container(
        //               padding: EdgeInsets.symmetric(horizontal: 7, vertical: 5),
        //               decoration: BoxDecoration(
        //                   color: Colors.white,
        //                   borderRadius: BorderRadius.circular(6),
        //                   border: Border.all(color: Colors.black, width: 1)),
        //               child: Row(
        //                 children: [
        //                   NarFormLabelWidget(
        //                       label:
        //                           '${widget.controller.value.playbackSpeed}x'),
        //                   SizedBox(width: 5),
        //                   Icon(Icons.keyboard_arrow_down)
        //                 ],
        //               )),
        //         ),
        //       ),
        //     ],
        //   ),
        // ),
        // Align(
        //   alignment: Alignment.topRight,
        //   child: Row(
        //     mainAxisSize: MainAxisSize.min,
        //     crossAxisAlignment: CrossAxisAlignment.center,
        //     children: [
        //       // NarFormLabelWidget(label: 'Playback Speed'),
        //       // SizedBox(width: 10,),
        //       PopupMenuButton<double>(
        //         initialValue: widget.controller.value.playbackSpeed,
        //         tooltip: 'Playback speed',
        //         onSelected: (double speed) {
        //           widget.controller.setPlaybackSpeed(speed);
        //           setState(() {});
        //         },
        //         itemBuilder: (BuildContext context) {
        //           return <PopupMenuItem<double>>[
        //             for (final double speed in videoPlaybackRates)
        //               PopupMenuItem<double>(
        //                 value: speed,
        //                 child: NarFormLabelWidget(label: '${speed}x'),
        //               )
        //           ];
        //         },
        //         child: Padding(
        //           padding: const EdgeInsets.symmetric(
        //             vertical: 12,
        //             horizontal: 16,
        //           ),
        //           child: Container(
        //               padding: EdgeInsets.symmetric(horizontal: 7, vertical: 5),
        //               decoration: BoxDecoration(
        //                   color: Colors.white,
        //                   borderRadius: BorderRadius.circular(6),
        //                   border: Border.all(color: Colors.black, width: 1)),
        //               child: Row(
        //                 children: [
        //                   NarFormLabelWidget(
        //                       label:
        //                           '${widget.controller.value.playbackSpeed}x'),
        //                   SizedBox(width: 5),
        //                   Icon(Icons.keyboard_arrow_down)
        //                 ],
        //               )),
        //         ),
        //       ),
        //     ],
        //   ),
        // ),
      ],
    );
  }
}
