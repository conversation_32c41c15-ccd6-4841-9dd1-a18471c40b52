
import 'dart:math';
import 'package:collection/collection.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/classes/fixedProperty.dart';
import 'package:newarc_platform/classes/newarcMaterial.dart';
import 'package:newarc_platform/classes/newarcProject.dart';
import 'package:newarc_platform/classes/process.dart';
import 'package:newarc_platform/classes/payments.dart';
import 'package:newarc_platform/classes/projectJob.dart';
import 'package:newarc_platform/classes/supplier.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:intl/intl.dart';
import '../../../classes/newarcProjectFixedAssetsPropertyPagamento.dart';
import '../../UI/base_newarc_popup.dart';
import '../../UI/file-picker.dart';
import '../../UI/tab/common_icon_button.dart';
import '../../UI/tab/icon_text_button.dart';

class ProjectPayments extends StatefulWidget {
  NewarcProject? project;
  final List<Supplier>? suppliers;
  final Function? updateProject;

  ProjectPayments({Key? key, this.project, this.suppliers, this.updateProject})
      : super(key: key);

  @override
  State<ProjectPayments> createState() => _ProjectPayments();
}

class _ProjectPayments extends State<ProjectPayments> {
  NumberFormat localCurrencyFormat =
      NumberFormat.currency(locale: 'it_IT', symbol: '\€', decimalDigits: 0);

  NumberFormat localCurrencyFormatMainWithDecimal = NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 2);

  bool loading = true;

  NewarcProject? newarcProject;

  String progressMessage = '';

  List<Payment> immobileCosts = [];
  List<List<String>> immobilePaths = [];
  List<Payment> agencyCosts = [];
  List<List<String>> agencyPaths = [];
  List<Map> _suppliers = [];


  /** All outgoing payment "uscite" section will be in this */
  List<Map> outgoingPaymentSections = [];


  /** 
   * This list will have Paid status of every row in the table
   * The Paid status of the row can be fetched with the rowCounter 
   */
  List<bool> isPaid = [];


  /** 
   * This list will have Payment date of every row in the table
   * The Payment date of the row can be fetched with the rowCounter 
   */
  List<int> datePaidValue = [];

  /** 
   * This list have the TextEditingController for row in the table
   * This controller is used for the Calendar date for every row.
   * Use rowCounter to get the controller
   */
  List<TextEditingController> contDatePaid = [];

  /**
   * This list have the TextEditingController for row in the table
   * This controller is used for the paidAmount for every row.
   * Use rowCounter to get the controller
   */
  List<TextEditingController> contPaidAmount = [];

  /** 
   * This list is used to set a message under the title of every row.
   * It is useful to display a message to the user while saving the data for the row
   * Use rowCounter to get the text
   */
  List<Map> savingRows = [];


  int savingIndex = -1;

  bool isRistrutturazione = false;

  double totalrevenue = 0;
  double paidRevenue = 0;
  double totalCosts = 0;
  double paidCosts = 0;

  Map<String, dynamic> tmpProjectData = {};

  @override
  void initState() {
    super.initState();

    isRistrutturazione = widget.project!.type == 'Ristrutturazione';

    _suppliers = widget.suppliers!.map((e) {
      return {'value': e.id, 'label': e.firstName! + ' ' + e.lastName!};
    }).toList();

    setInitialValues();

    // fetchProject();
  }

  @protected
  void didUpdateWidget(ProjectPayments oldWidget) {
    super.didUpdateWidget(oldWidget);

    // setInitialValues();
  }

  setInitialValues() async {
    DocumentSnapshot<Map<String, dynamic>> collectionSnapshot;

    collectionSnapshot = await FirebaseFirestore.instance
        .collection(appConfig.COLLECT_NEWARC_PROJECTS)
        .doc(widget.project!.id!)
        .get();

    if (collectionSnapshot.data() != null) {
      newarcProject = NewarcProject.fromDocument(
          collectionSnapshot.data()!, widget.project!.id!);
    }

    /**
     * This rowCounter plays a vital role in setting an index for every row of payments
     * When adding a new Row make sure to add and increment the rowCounter
     */
    int rowsCounter = 0;

    List<Map> vendorsAndProfessionals = [];
    List<Map> manageMaterial = [];

    /**
     * The Vendor and Professional section has Payments in Instalments
     * This code is going through every element and checking for the Instalments
     * Every Instalment is considered as a new row 
     * So, this code will set a rowCounter for every row
     */
    if (newarcProject!.vendorAndProfessionals != null) {
      for (var i = 0; i < newarcProject!.vendorAndProfessionals!.length; i++) {
        if (newarcProject!.vendorAndProfessionals![i].installments != null &&
            newarcProject!.vendorAndProfessionals![i].installments!.length >
                0) {
          for (var j = 0;
              j <
                  widget
                      .project!.vendorAndProfessionals![i].installments!.length;
              j++) {
            PaymentInstallment installment =
                newarcProject!.vendorAndProfessionals![i].installments![j];

            bool status = installment.status == null ? false : installment.status!;

            isPaid.add(status);
            savingRows.add({'message': ''});
            contDatePaid.add(new TextEditingController());
            contPaidAmount.add(new TextEditingController());
            datePaidValue.add(installment.paidOn ?? 0);

            String vendorId =
                newarcProject!.vendorAndProfessionals![i].vendorUserId!;

            var supplier = widget.suppliers?.firstWhere(
                  (e) => e.id == vendorId,
            );

            String label = '';

            if (supplier != null) {
              label = '${supplier.name ?? ''} ${supplier.formationType ?? ''}';
            }

            label = "$label - Rata ${j + 1}";

            vendorsAndProfessionals.add({
              'rowCounter': rowsCounter,
              'path': 'vendorAndProfessionals/$i/installments/$j',
              'switchPath': 'vendorAndProfessionals/$i/installments/$j/status',
              'datePath': 'vendorAndProfessionals/$i/installments/$j/paidOn',
              'key': 'vendorAndProfessionals/$i/installments/$j',
              'title': label,
              'total': newarcProject!.vendorAndProfessionals![i].cost,
              'value': installment.amount,
              'isPaid': isPaid[rowsCounter],
              'date': contDatePaid[rowsCounter],
              'eog': j == (newarcProject!.vendorAndProfessionals![i].installments!.length - 1)
                  ? true
                  : false,
              'sog': j == 0 ? true : false,
              'invoicePath': (installment.invoicePath != null && (installment.invoicePath as Map).isNotEmpty)
                  ? installment.invoicePath
                  : null,
              'invoiceImages': (installment.invoicePath != null && (installment.invoicePath as Map).isNotEmpty)
                  ? [installment.invoicePath?["filename"]]
                  : [],
              'isInvoiceUploaded': (installment.invoicePath != null && (installment.invoicePath as Map).isNotEmpty),
              'uniqueId': installment.uniqueId,
              'paidAmount': installment.paidAmount,
            });

            rowsCounter++;
          }
        }
        // List<Map> PaymentInstalment = [];
      }
    }

    /**
     * Newarc Material class has Payment in Rows 
     * So it is important to fetch all the Rows
     * This code will give a path and fetch the values from the NewarcMaterial Class
     */
    if (newarcProject!.newarcMaterial != null) {
      for (var i = 0; i < newarcProject!.newarcMaterial!.length; i++) {
        bool status = newarcProject!.newarcMaterial![i].isPaid == null
            ? false
            : newarcProject!.newarcMaterial![i].isPaid!;

        savingRows.add({'message': ''});
        isPaid.add(status);
        contDatePaid.add(new TextEditingController());
        datePaidValue.add(newarcProject!.newarcMaterial?[i].paidOn ?? 0);

        double cost;
        if (newarcProject!.newarcMaterial![i].isAccount == true) {
          cost = newarcProject!.newarcMaterial![i].cost! -
              newarcProject!.newarcMaterial![i].account!;
        } else {
          cost = newarcProject!.newarcMaterial![i].cost!;
        }

        manageMaterial.add({
          'rowCounter': rowsCounter,
          'path': 'newarcMaterial/$i',
          'switchPath': 'newarcMaterial/$i/isPaid',
          'datePath': 'newarcMaterial/$i/paidOn',
          'key': 'newarcMaterial/$i',
          'title': newarcProject!.newarcMaterial![i].productName,
          'total': newarcProject!.newarcMaterial![i].cost,
          'value': cost,
          'isPaid': isPaid[rowsCounter],
          'date': contDatePaid[rowsCounter],
          'sog': true,
          'eog': false,
          'invoicePath': (newarcProject!.newarcMaterial![i].invoicePath != null && (newarcProject!.newarcMaterial![i].invoicePath as Map).isNotEmpty)
              ? newarcProject!.newarcMaterial![i].invoicePath
              : null,
          'invoiceImages': (newarcProject!.newarcMaterial![i].invoicePath != null && (newarcProject!.newarcMaterial![i].invoicePath as Map).isNotEmpty)
              ? [newarcProject!.newarcMaterial![i].invoicePath?["filename"]]
              : [],
          'isInvoiceUploaded': (newarcProject!.newarcMaterial![i].invoicePath != null && (newarcProject!.newarcMaterial![i].invoicePath as Map).isNotEmpty),
          'uniqueId': newarcProject!.newarcMaterial![i].uniqueId,
          'paidAmount': newarcProject!.newarcMaterial![i].paidAmount,
        });

        if (newarcProject!.newarcMaterial![i].isAccount == true) {
          bool _status = newarcProject!.newarcMaterial![i].isAccountPaid == null
              ? false
              : newarcProject!.newarcMaterial![i].isAccountPaid!;

          savingRows.add({'message': ''});
          isPaid.add(_status);
          contDatePaid.add(new TextEditingController());
          datePaidValue.add(newarcProject!.newarcMaterial![i].accountPaidOn ?? 0);

          rowsCounter++;

          manageMaterial.add({
            'rowCounter': rowsCounter,
            'path': 'newarcMaterial/$i',
            'switchPath': 'newarcMaterial/$i/isAccountPaid',
            'datePath': 'newarcMaterial/$i/accountPaidOn',
            'key': 'newarcMaterial/$i',
            'title': 'Acconto',
            'total': newarcProject!.newarcMaterial![i].cost,
            'value': newarcProject!.newarcMaterial![i].account,
            'isPaid': isPaid[rowsCounter],
            'date': contDatePaid[rowsCounter],
            'sog': false,
            'eog': false,
            'invoicePath': (newarcProject!.newarcMaterial![i].invoicePath != null && (newarcProject!.newarcMaterial![i].invoicePath as Map).isNotEmpty)
                ? newarcProject!.newarcMaterial![i].invoicePath
                : null,
            'invoiceImages': (newarcProject!.newarcMaterial![i].invoicePath != null && (newarcProject!.newarcMaterial![i].invoicePath as Map).isNotEmpty)
                ? [newarcProject!.newarcMaterial![i].invoicePath?["filename"]]
                : [],
            'isInvoiceUploaded': (newarcProject!.newarcMaterial![i].invoicePath != null && (newarcProject!.newarcMaterial![i].invoicePath as Map).isNotEmpty),
            'uniqueId': newarcProject!.newarcMaterial![i].uniqueId,
            'paidAmount': newarcProject!.newarcMaterial![i].paidAmount,
          });
        }

        rowsCounter++;
      }
    }


    /** 
     * Refurbisment cost from Immobile
     * */
    Map fixedAssets = {};
    if (newarcProject!.type == 'Ristrutturazione') {
      List<NewarcProjectFixedAssetsPropertyPagamento> newPagamento = [];

      for (NewarcProjectFixedAssetsPropertyPagamento pagamento in newarcProject?.fixedProperty?.newarcProjectFixedAssetsPropertyPagamento ?? []) {

        if(!(pagamento.isManualCategory ?? false)){
          DocumentSnapshot<Map<String, dynamic>> collectionQuery = await FirebaseFirestore.instance
              .collection(appConfig.COLLECT_NEWARCPROJECTFIXEDASSETSPROPERTYCATEGORY)
              .doc(pagamento.newarcProjectFixedAssetsPropertyCategoryId)
              .get();
          if (collectionQuery.exists) {
            pagamento.categoryName = collectionQuery.data()?["name"];
          }
        }


        for (var rate in pagamento.rate ?? []) {
          DocumentSnapshot<Map<String, dynamic>> perQuery = await FirebaseFirestore.instance
              .collection(appConfig.COLLECT_NEWARCPROJECTFIXEDASSETSPROPERTYPERCENTAGE)
              .doc(rate.newarcProjectFixedAssetsPercentageId)
              .get();
          if (perQuery.exists) {
            rate.percentage = perQuery.data()?["percentage"];
          }
        }
        newPagamento.add(pagamento);
      }

      newarcProject?.fixedProperty?.newarcProjectFixedAssetsPropertyPagamento = newPagamento;
      widget.project?.fixedProperty?.newarcProjectFixedAssetsPropertyPagamento = newPagamento;
    } else {
      fixedAssets = {
        'deposit': 'Caparra',
        'balance': 'Saldo immobile',
        'notaryCost': 'Costi notarili',
        'registrationTax': 'Imposta di registro',
        'ape': 'APE',
        'condominiumFees': 'Spese condominiali',
        'loadUnloadInstallation': 'Carico/Scarico impianti',
        'registrationTax2': 'Registration Tax 2',
        'electricalCond': 'Elettricista cond.',
        'light': 'Luce',
        'moveGasCont': 'Spost. cont gas',
        'heating': 'Riscaldamento',
        'weldingCost': 'Opere di saldatura',
        'carInsurance': 'Assicurazione C.A.R.',
        'posthumousInsurance': 'Assicurazione Postuma',
      };
    }

    /**
     * Setting the outgoing payment sections and their rows
     *
     * key: is the name of the main column i.e. Class  
     * fields: The Fiels Map has { "column name in collection" : "Label you wanted to display" }
     * rows: It's a List<Map>. Based on the fields the Rows will be set in the Map. 
     * Child of rows: 
     * {
     * 'rowCounter': It a Unique id for every row in the map. It's used when doing any operation,
     * 'path': It's path of the row in the collection  assignedAgency/bonusIns, This path will be used to save the data
     * 'switchPath': This path to the paid status in the colleciton. It is used to toggle switch i.e. assignedAgency/bonusInsPaid
     * 'datePath': This path to the Payment date in the colleciton i.e. assignedAgency/bonusInsPaidDate
     * 'key': key,
     * 'title':Label that will displayed for the Row,
     * 'total': Total that will be displayed in the costo tot.,
     * 'value': Total value if it's single payment, instalment if the payment is divided,
     * 'isPaid': bool element to set the status of the payment (switch button),
     * 'date': TextEditingContoller for the Calender,
     * 'eog': End of Group is used with the payment are in instalment. It will determine if the intallment group is ended. It will remain true if it's a lum sum payment,
     * 'sog': Start of Group is used for the instalment payments, We are displaying the Total payment with the first row only so it will be used for that purpose.
     * }
     */
    outgoingPaymentSections = [
      {
        'title': newarcProject!.type == 'Ristrutturazione'
            ? 'Pagamenti ristrutturazione'
            : 'Costi immobile',
        'key': 'fixedProperty',
        'fields': fixedAssets,
        'rows': []
      },
      {
        'title': 'Agenzia',
        'key': 'assignedAgency',
        'fields': {
          'bonusIns': 'Bonus In',
          'bonusObi': 'Bonus Obi',
          'commissionIn': 'Commissione In',
          'commissionOut': 'Commissione Out'
        },
        'rows': []
      },
      {
        'title': 'Ditte e professionisti',
        'key': 'vendorAndProfessionals',
        'rows': vendorsAndProfessionals
      },
      {
        'title': 'Gestione materiali',
        'key': 'newarcMaterial',
        'rows': manageMaterial
      }
    ];

    if (isRistrutturazione) {
      outgoingPaymentSections.removeWhere((section) =>
          section['key'] == 'fixedProperty' ||
          section['key'] == 'assignedAgency');
    }

    Map projectData = newarcProject!.toMap();

    outgoingPaymentSections.forEachIndexed((sectionIndex, section) {
      if (section['fields'] != null) {
        section['fields'].forEach((key, value) {
          if (value is List) {
            for (var child1Key = 0; child1Key < value.length; child1Key++) {
              bool status =
                  projectData[section['key']][key][child1Key]['Paid'] == null
                      ? false
                      : projectData[section['key']][key][child1Key]['Paid'];

              savingRows.add({'message': ''});

              isPaid.add(status);
              contDatePaid.add(new TextEditingController());
              datePaidValue.add(projectData[section['key']][key][child1Key]['PaidDate'] ?? 0);

              Map _data = {
                'rowCounter': rowsCounter,
                'path': section['key'] + '/' + key + '/' + child1Key.toString(),
                'switchPath': section['key'] +
                    '/' +
                    key +
                    '/' +
                    child1Key.toString() +
                    '/' +
                    'Paid',
                'datePath': section['key'] +
                    '/' +
                    key +
                    '/' +
                    child1Key.toString() +
                    '/' +
                    'PaidDate',
                'key': child1Key.toString(),
                'title': projectData[section['key']][key][child1Key]['label'].toString(),
                'total': projectData[section['key']][key][child1Key]['total'].toString(),
                'value': projectData[section['key']][key][child1Key]['value'].toString(),
                'paidAmount': projectData[section['key']][key][child1Key]['paidAmount'].toString(),
                'uniqueId': projectData[section['key']][key][child1Key]['uniqueId'].toString(),
                'isInvoiceUploaded': projectData[section['key']][key][child1Key]['isInvoiceUploaded'],
                'invoiceImages': projectData[section['key']][key][child1Key]['invoiceImages'],
                'invoicePath': projectData[section['key']][key][child1Key]['invoicePath'],
                'isPaid': isPaid[rowsCounter],
                'date': contDatePaid[rowsCounter],
                'eog': child1Key == (value.length - 1) ? true : false,
                'sog': child1Key == 0 ? true : false,
              };

              outgoingPaymentSections[sectionIndex]['rows'].add(_data);

              rowsCounter++;
            }
          } else if (projectData[section['key']][key] != null) {
            bool status = projectData[section['key']][key + 'Paid'] == null
                ? false
                : projectData[section['key']][key + 'Paid'];

            savingRows.add({'message': ''});
            isPaid.add(status);
            contDatePaid.add(new TextEditingController());
            datePaidValue.add(projectData[section['key']][key + 'PaidDate'] ?? 0);

            outgoingPaymentSections[sectionIndex]['rows'].add({
              'rowCounter': rowsCounter,
              'path': section['key'] + '/' + key,
              'switchPath': section['key'] + '/' + key + 'Paid',
              'datePath': section['key'] + '/' + key + 'PaidDate',
              'key': key,
              'title': value,
              'total': projectData[section['key']][key],
              'value': projectData[section['key']][key],
              'paidAmount': projectData[section['key']][key]['paidAmount'].toString(),
              'uniqueId': projectData[section['key']][key]['uniqueId'].toString(),
              'isInvoiceUploaded': projectData[section['key']][key]['isInvoiceUploaded'],
              'invoiceImages': projectData[section['key']][key]['invoiceImages'],
              'invoicePath': projectData[section['key']][key]['invoicePath'],
              'isPaid': isPaid[rowsCounter],
              'date': contDatePaid[rowsCounter],
              'eog': false,
              'sog': true,
            });

            rowsCounter++;
          }
        });
      }
    });

    getProgressTotal();

    setState(() {
      loading = false;
    });
  }

  /**
   * This method will find the totals for both
   * Entrate and Uscite
   */
  getProgressTotal() {
    totalCosts = 0;
    paidCosts = 0;
    totalrevenue = 0;
    paidRevenue = 0;

    outgoingPaymentSections.forEach((section) {
      if (section['rows'].length > 0) {
        for (var i = 0; i < section['rows'].length; i++) {
          Map row = section['rows'][i];
          totalCosts += double.tryParse(row['value'].toString()) ?? 0;
          if (row['isPaid']) {
            paidCosts += double.tryParse(row['value'].toString()) ?? 0;
          }
        }
      }
    });

    for (var pagamento in newarcProject?.fixedProperty?.newarcProjectFixedAssetsPropertyPagamento ?? []) {
      totalrevenue += (pagamento.total + pagamento.totalIVA);
      for (Rate rate in pagamento.rate ?? []) {
        paidRevenue += (rate.paidAmount ?? 0);
      }
    }
  }

  void getAgencyCosts(FixedProperty fixedProperty) {}

  void getImmobileCosts(FixedProperty fixedProperty) {}

  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Container(
      height: double.infinity,
      child: loading == true
          ? Center(child: NarFormLabelWidget(label: 'Loading'))
          : Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.max,
              children: [
                Expanded(
                  child: Form(
                    key: _formKey,
                    child: ListView(
                      children: [
                        SizedBox(height: 20),
                        NarFormLabelWidget(
                          label: 'Pagamenti',
                          fontSize: 20,
                          fontWeight: 'bold',
                        ),
                        SizedBox(height: 20),
                        isRistrutturazione
                            ? LayoutBuilder(
                                builder: (BuildContext context, BoxConstraints constraints) {
                                  double maxWidth = constraints.constrainWidth();
                                  return Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10),
                                      border: Border.all(color: Color(0xFFDADADA)),
                                    ),
                                    padding: EdgeInsets.all(15),
                                    child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              NarFormLabelWidget(
                                                label: 'Entrate',
                                                fontSize: 17,
                                                fontWeight: 'bold',
                                              ),
                                              NarFormLabelWidget(
                                                label: localCurrencyFormat.format(totalrevenue),
                                                fontSize: 17,
                                                fontWeight: 'bold',
                                              ),
                                            ],
                                          ),
                                          SizedBox(height: 5),
                                          Stack(
                                            children: [
                                              Container(
                                                height: 22,
                                                decoration: BoxDecoration(
                                                    color: Color(0xffD9D9D9),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            50)),
                                              ),
                                              Container(
                                                height: 22,
                                                width: (paidRevenue == 0 ||
                                                    totalrevenue == 0)
                                                    ? 0
                                                    : maxWidth * (paidRevenue / totalrevenue),
                                                decoration: BoxDecoration(
                                                    color: Theme.of(context)
                                                        .primaryColor,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            50)),
                                                alignment: Alignment.centerRight,
                                                padding: EdgeInsets.symmetric(
                                                    horizontal: 5),
                                                child: NarFormLabelWidget(
                                                  label: localCurrencyFormat
                                                      .format(paidRevenue),
                                                  textColor: Colors.white,
                                                  fontSize: 12,
                                                  fontWeight: '500',
                                                ),
                                              ),
                                            ],
                                          ),
                                          SizedBox(height: 10),
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              NarFormLabelWidget(
                                                label: 'Uscite',
                                                fontSize: 17,
                                                fontWeight: 'bold',
                                              ),
                                              NarFormLabelWidget(
                                                label: localCurrencyFormat
                                                    .format(totalCosts),
                                                fontSize: 17,
                                                fontWeight: 'bold',
                                              ),
                                            ],
                                          ),
                                          SizedBox(height: 5),
                                          Stack(
                                            children: [
                                              Container(
                                                height: 22,
                                                decoration: BoxDecoration(
                                                  color: Color(0xffD9D9D9),
                                                  borderRadius:
                                                      BorderRadius.circular(50),
                                                ),
                                              ),
                                              Container(
                                                height: 22,
                                                width: (paidCosts == 0 ||
                                                        totalCosts == 0)
                                                    ? 0
                                                    : maxWidth *
                                                        (paidCosts / totalCosts),
                                                decoration: BoxDecoration(
                                                    color: Color(0xffDB0000),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            50)),
                                                alignment: Alignment.centerRight,
                                                padding: EdgeInsets.symmetric(
                                                    horizontal: 5),
                                                child: NarFormLabelWidget(
                                                  label: localCurrencyFormat
                                                      .format(paidCosts),
                                                  textColor: Colors.white,
                                                  fontSize: 12,
                                                  fontWeight: '500',
                                                ),
                                              ),
                                            ],
                                          ),
                                        ]),
                                  );
                                },
                              )
                            : Container(),
                        SizedBox(height: 30),
                        isRistrutturazione
                            ? Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                border: Border.all(color: Color(0xFFDADADA)),
                              ),
                              padding: EdgeInsets.all(10),
                              child: LayoutBuilder(
                                builder: (BuildContext context, BoxConstraints constraints) {
                                  double maxWidth = constraints.constrainWidth();
                                  return entrateList(context, newarcProject?.fixedProperty?.newarcProjectFixedAssetsPropertyPagamento ?? []);
                                },
                              ),
                            )
                            : Container(),
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(color: Color(0xFFDADADA)),
                          ),
                          margin: EdgeInsets.only(top: 30),
                          padding: EdgeInsets.all(10),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              NarFormLabelWidget(
                                label: isRistrutturazione ? 'Uscite' : 'Pagamenti',
                                fontSize: 20,
                                fontWeight: 'bold',
                              ),
                              SizedBox(height: 10),
                              LayoutBuilder(
                                builder: (BuildContext context, BoxConstraints constraints) {
                                  double maxWidth = constraints.constrainWidth();
                                  return Column(
                                    children: outgoingPaymentSections
                                        .map((section) => processWrapper(
                                        context, section, maxWidth,
                                        isIncomingRevenue: false))
                                        .toList(),
                                  );
                                },
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ],
            ),
    );
  }

  List<DataColumn> getColumns(double pageWidth, {isIncomingRevenue}) {
    List<DataColumn> list = [];

    list.add(DataColumn2(
        fixedWidth: isIncomingRevenue ? 0.35 * pageWidth : 0.4 * pageWidth,
        label: Container(
          child: NarFormLabelWidget(
            label: 'Oggetto',
            fontSize: 13,
            fontWeight: '500',
            textColor: Color.fromRGBO(131, 131, 131, 1),
          ),
        )));

    list.add(DataColumn2(
        fixedWidth: 0.15 * pageWidth,
        // size: ColumnSize.S,
        label: NarFormLabelWidget(
          label: 'Pagamenti',
          fontSize: 13,
          fontWeight: '500',
          textColor: Color.fromRGBO(131, 131, 131, 1),
        )));
    list.add(DataColumn2(
        fixedWidth: 0.1 * pageWidth,
        label: NarFormLabelWidget(
          label: 'Pagato',
          fontSize: 13,
          fontWeight: '500',
          textColor: Color.fromRGBO(131, 131, 131, 1),
        )));
    list.add(DataColumn2(
        fixedWidth: 0.15 * pageWidth,
        // size: ColumnSize.S,
        label: NarFormLabelWidget(
          label: 'Data',
          fontSize: 13,
          fontWeight: '500',
          textColor: Color.fromRGBO(131, 131, 131, 1),
        )));
    list.add(DataColumn2(
        fixedWidth: 0.15 * pageWidth,
        label: NarFormLabelWidget(
          label: 'Fattura',
          fontSize: 13,
          fontWeight: '500',
          textColor: Color.fromRGBO(131, 131, 131, 1),
        )));
    list.add(DataColumn2(
        fixedWidth: 0.1 * pageWidth,
        label: NarFormLabelWidget(
          label: '',
          fontSize: 13,
          fontWeight: '500',
          textColor: Color.fromRGBO(131, 131, 131, 1),
        )));

    return list;
  }

  Widget processWrapper(BuildContext context, Map processRow, double pageWidth, {isIncomingRevenue}) {
    List<DataRow> rows = [];
    if (processRow['rows'].length > 0) {
      for (var i = 0; i < processRow['rows'].length; i++) {
        rows.add(DataRow(
          cells: getDataRow(processRow['rows'][i],
              isIncomingRevenue: isIncomingRevenue),
        ));

        /* Add empty row */
        if (processRow['rows'][i]['eog'] == true) {
          if (!isIncomingRevenue) {
            rows.add(DataRow(cells: [
              DataCell(SizedBox(height: 0)),
              DataCell(SizedBox(height: 0)),
              DataCell(SizedBox(height: 0)),
              DataCell(SizedBox(height: 0)),
              DataCell(SizedBox(height: 0)),
              DataCell(SizedBox(height: 0)),
            ]));
            
          } else {
            rows.add(DataRow(cells: [
              DataCell(SizedBox(height: 0)),
              DataCell(SizedBox(height: 0)),
              DataCell(SizedBox(height: 0)),
              DataCell(SizedBox(height: 0)),
              DataCell(SizedBox(height: 0)),
              DataCell(SizedBox(height: 0)),
            ]));
            
          }
        }
      }
    }

    return Column(
      children: [
        Container(
          margin: EdgeInsets.only(bottom: 25),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 15.0, left: 20, bottom: 10),
                child: NarFormLabelWidget(
                  label: processRow['title'],
                  fontSize: 17,
                  fontWeight: '700',
                  textColor: Color.fromRGBO(0, 0, 0, 1),
                ),
              ),
              Container(
                height: loading
                    ? (300 * (rows.length + 0.5))
                    : (50 * (rows.length + 0.5)),
                child: DataTable2(
                  key: widget.key,
                  // dataRowHeight: loading ? 300 : 70,
                  isHorizontalScrollBarVisible: false,
                  minWidth: max(pageWidth, 1500),
                  columnSpacing: 10,
                  dividerThickness: 0,
                  horizontalMargin: 20,
                  headingRowHeight: 25,
                  columns: getColumns(pageWidth,
                      isIncomingRevenue: isIncomingRevenue),
                  rows: rows,
                ),
              ),
              SizedBox(height: 10),
            ],
          ),
        )
      ],
    );
  }

  Widget entrateList(BuildContext context, List<NewarcProjectFixedAssetsPropertyPagamento> pagamenti) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        NarFormLabelWidget(
          label: 'Entrate',
          fontSize: 20,
          fontWeight: 'bold',
        ),
        SizedBox(height: 10),
        ...pagamenti.map((section) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ...section.rate!
                  .where((rate) => !(rate.isMerged ?? false)) //---->>>> Skip merged rates
                  .map((rate) {
                rate.category = section.categoryName;
                return entrateRow(context, rate, section);
              }).toList()
            ],
          );
        }).toList()
      ],
    );
  }

  Widget entrateRow(BuildContext context, Rate rate,NewarcProjectFixedAssetsPropertyPagamento pagamento) {
    final isMerged = rate.margeRateUniqueIDS?.isNotEmpty ?? false;

    Color getBGColor(String status){
      switch (status) {
        case 'in attesa':
          return Colors.transparent;
        case 'parzialmente pagato':
          return Colors.transparent;
        case 'pagato':
          return Color(0xFFE7F6F0);
        default:
          return Colors.transparent;
      }
    }

    Border getBoarder(String status){
      switch (status) {
        case 'in attesa':
          return Border.all(color: Color(0xFFDADADA),width: 1);
        case 'parzialmente pagato':
          return Border.all(color: Color(0xFFDADADA),width: 1);
        case 'pagato':
          return Border.all(color: Colors.transparent,width: 0);
        default:
          return Border.all(color: Color(0xFFDADADA),width:  1);
      }
    }
    return Container(
      margin: EdgeInsets.only(bottom: 10),
      padding: EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: getBGColor(rate.paymentStatus ?? ""),
        border: getBoarder(rate.paymentStatus ?? ""),
        borderRadius: BorderRadius.circular(7),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                statusIconForEtrate(rate.paymentStatus ?? ""),
                SizedBox(width: 10),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    NarFormLabelWidget(
                      label: "${rate.category} - Rata ${rate.index}",
                      fontWeight: '500',
                      fontSize: 12,
                      textColor: Color(0xFF707070),
                    ),
                    SizedBox(height: 5,),
                    NarFormLabelWidget(label: rate.description ?? "", fontSize: 12,fontWeight: "600",),
                    if (isMerged)
                      ...rate.margeRateUniqueIDS!.map((id) {
                        final mergedRate = findRateById(id); // implement this function
                        return mergedRate != null
                            ? Padding(
                          padding: const EdgeInsets.only(top: 15.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              NarFormLabelWidget(
                                label: "${mergedRate.category} - Rata ${mergedRate.index}",
                                fontWeight: '500',
                                fontSize: 12,
                                textColor: Color(0xFF707070),
                              ),
                              SizedBox(height: 5,),
                              NarFormLabelWidget(
                                label: mergedRate.description ?? "",
                                fontSize: 12,fontWeight: "600",
                              ),
                            ],
                          ),
                        )
                            : SizedBox.shrink();
                      }).toList(),
                  ],
                ),
              ],
            ),
          ),
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      NarFormLabelWidget(
                        label: 'Da pagare',
                        fontSize: 12,
                        fontWeight: '500',
                        textColor: Color(0xFF707070),
                      ),
                      SizedBox(height: 5,),
                      NarFormLabelWidget(
                        label: "${localCurrencyFormatMainWithDecimal.format(((rate.rate ?? 0)+(rate.ivaAmount ?? 0)))}€",
                        fontSize: 12,fontWeight: "600",
                      ),
                      if (isMerged)
                        ...rate.margeRateUniqueIDS!.map((id) {
                          final mergedRate = findRateById(id); // implement this function
                          return mergedRate != null
                              ? Padding(
                            padding: const EdgeInsets.only(top: 15.0),
                            child: NarFormLabelWidget(
                              label: "${localCurrencyFormatMainWithDecimal.format(((mergedRate.rate ?? 0) + (mergedRate.ivaAmount ?? 0)))}€",
                              fontSize: 12,fontWeight: "600",
                            ),
                          )
                              : SizedBox.shrink();
                        }).toList(),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      NarFormLabelWidget(
                        label: 'Pagato',
                        fontSize: 12,
                        fontWeight: '500',
                        textColor: Color(0xFF707070),
                      ),
                      SizedBox(height: 5,),
                      NarFormLabelWidget(
                        label: "${localCurrencyFormatMainWithDecimal.format(rate.paidAmount ?? 0)}€",
                        fontSize: 12,fontWeight: "600",
                      )
                    ],
                  ),
                ),
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButtonWidget(
                      onTap: () {
                        showAddEditPagamentoPopup(pagamento,rate);
                      },
                      isSvgIcon: true,
                      icon: 'assets/icons/edit.svg',
                      backgroundColor: Color(0xFF4D4D4D),
                      iconColor: AppColor.white,
                      borderRadius: 7,
                      height: 27,
                      width: 27,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Rate? findRateById(String id) {
    for (var section in widget.project?.fixedProperty?.newarcProjectFixedAssetsPropertyPagamento ?? []) {
      for (var rate in section.rate ?? []) {
        if (rate.uniqueId == id){
          rate.category = section.categoryName;
          return rate;
        }
      }
    }
    return null;
  }

  Widget statusIconForEtrate(String status) {
    final double iconSize = 12.0;
    final double containerSize = 16.0;

    Color? backgroundColor;
    Gradient? backgroundGradient;
    IconData iconData;
    Color iconColor = Colors.white;

    switch (status) {
      case 'in attesa':
        backgroundColor = Color(0xffD1D1D1);
        iconData = Icons.close_rounded;
        break;
      case 'parzialmente pagato':
        backgroundGradient = const LinearGradient(
          colors: [
            Color(0xFF4E9A7A),
            Color(0xFFB5D8C9),
          ],
          stops: [0.5, 0.5],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        );
        iconData = Icons.check;
        break;
      case 'pagato':
        backgroundColor = Theme.of(context).primaryColor;
        iconData = Icons.check;
        break;
      default:
        backgroundColor = const Color(0xffD1D1D1);
        iconData = Icons.close_rounded;
    }

    return Container(
      height: containerSize,
      width: containerSize,
      decoration: BoxDecoration(
        color: backgroundColor,
        gradient: backgroundGradient,
        borderRadius: BorderRadius.circular(14),
      ),
      child: Center(
        child: Icon(
          iconData,
          color: iconColor,
          size: iconSize,
        ),
      ),
    );
  }

  Widget statusIcon(bool status) {
    return Container(
      height: 16,
      width: 16,
      decoration: BoxDecoration(
        color: status ? Theme.of(context).primaryColor : Color(0xffD1D1D1),
        borderRadius: BorderRadius.circular(14),
      ),
      child: Center(
        child: Icon(
          status ? Icons.check : Icons.close_rounded,
          color: Colors.white,
          size: 12,
        ),
      ),
    );
  }
  
  /**
   * This method will search for the key in the Map 
   * If found then return the value from the Map  
   * Else return null
   */
  dynamic getValueFromPath(Map<String, dynamic> data, List<String> path) {
    dynamic current = data;
    for (String key in path) {
      if (current is Map<String, dynamic> && current.containsKey(key)) {
        current = current[key];
      } else {
        return null; // Return null if path is invalid
      }
    }
    return current;
  }

  /**
   * This method will search for the key in the Map 
   * If found set the value 
   * Else create a new element and set the value
   */

  void setValueInNestedMap(Map map, List<String> path, dynamic value) {
    dynamic current = map;
    try {
      for (int i = 0; i < path.length - 1; i++) {
        String key = path[i];
        dynamic keyToUse = int.tryParse(key) ?? key; // Handle numeric keys

        if (i == path.length - 1) {
          current[keyToUse] = value; // Set the value at the final key
        } else {
          if (current[keyToUse] == null) {
            current[keyToUse] =
                {}; // Create a new map if the path does not exist
          }
          current = current[keyToUse];
        }
      }
      // Set the value at the last key in the path
      current[path.last] = value;
    } catch (e, s) {
      // print({e, s});
    }
  }

  saveProject(data) async {
    int rowcounter = data['rowCounter'];
      try{
        tmpProjectData = newarcProject!.toMap();

        setState(() {
          savingRows[rowcounter]['message'] = 'Salvataggio in corso...';
        });

        List<String> switchPath = data['switchPath'].toString().split('/');
        List<String> datePath = data['datePath'].toString().split('/');


        setValueInNestedMap(tmpProjectData, switchPath, isPaid[rowcounter]);
        setValueInNestedMap(tmpProjectData, datePath, datePaidValue[rowcounter]);

        FixedProperty fixedProperty = FixedProperty(tmpProjectData['fixedProperty']);

        fixedProperty = newarcProject!.fixedProperty!;

        tmpProjectData['fixedProperty'] = fixedProperty;

        List<NewarcMaterial> newarcMaterial = [];
        for (var i = 0; i < tmpProjectData['newarcMaterial'].length; i++) {
          NewarcMaterial tmpMaterial = NewarcMaterial(tmpProjectData['newarcMaterial'][i]);
          if (tmpMaterial.uniqueId == data['uniqueId']) {
            tmpMaterial.paidAmount = double.tryParse(data['paidAmount'].toString()) ?? 0;
            tmpMaterial.paidOn = datePaidValue[rowcounter];
            tmpMaterial.invoicePath = data['invoicePath'] ?? {};
            tmpMaterial.isPaid = isPaid[rowcounter];
          }
          newarcMaterial.add(tmpMaterial);
        }

        tmpProjectData['newarcMaterial'] = newarcMaterial;

        newarcProject!.newarcMaterial = newarcMaterial;

        List<ProjectJob> projectJobs = [];
        for (var i = 0; i < tmpProjectData['projectJobs'].length; i++) {
          List<JobComments> jobComments = [];
          if (tmpProjectData['projectJobs'][i]['jobComments'].length > 0) {
            for (var j = 0;
            j < tmpProjectData['projectJobs'][i]['jobComments'].length;
            j++) {
              jobComments.add(
                  JobComments(tmpProjectData['projectJobs'][i]['jobComments'][j]));
            }
          }
          tmpProjectData['projectJobs'][i]['jobComments'] = jobComments;

          projectJobs.add(ProjectJob(tmpProjectData['projectJobs'][i]));
        }
        tmpProjectData['projectJobs'] = projectJobs;
        newarcProject!.projectJobs = projectJobs;



        List<Process> vendorsAndProfessionals = [];

        for (var i = 0; i < tmpProjectData['vendorAndProfessionals'].length; i++) {
          List<PaymentInstallment> installments = [];
          for (var j = 0; j < tmpProjectData['vendorAndProfessionals'][i]['installments'].length; j++) {
            var installmentMap = tmpProjectData['vendorAndProfessionals'][i]['installments'][j];

            // --- Important: Update the correct installment before adding ---
            if (installmentMap['uniqueId'] == data['uniqueId']) {
              installmentMap['paidAmount'] = double.tryParse(data['paidAmount'].toString()) ?? 0;
              installmentMap['paidOn'] = datePaidValue[rowcounter];
              installmentMap['invoicePath'] = data['invoicePath'] ?? {};
              installmentMap['isPaid'] = isPaid[rowcounter];
            }

            installmentMap['counter'] = j + 1;
            installmentMap['isEndOfList'] = j == (tmpProjectData['vendorAndProfessionals'][i]['installments'].length - 1);

            installments.add(PaymentInstallment(installmentMap));
          }

          tmpProjectData['vendorAndProfessionals'][i]['installments'] = installments;

          vendorsAndProfessionals.add(Process(tmpProjectData['vendorAndProfessionals'][i]));
        }
        tmpProjectData['vendorAndProfessionals'] = vendorsAndProfessionals;

        newarcProject!.vendorAndProfessionals = vendorsAndProfessionals;

        tmpProjectData['agreements'] = newarcProject!.agreements;
        tmpProjectData['userNotifications'] = newarcProject!.userNotifications;



        NewarcProject tmpProjectObject = new NewarcProject(tmpProjectData);

        tmpProjectObject.id = newarcProject!.id;
        widget.project = tmpProjectObject;
        final FirebaseFirestore _db = FirebaseFirestore.instance;
        await _db
            .collection(appConfig.COLLECT_NEWARC_PROJECTS)
            .doc(widget.project!.id)
            .update(tmpProjectObject.toMap());

        setState(() {
          progressMessage = 'Saved!';
        });

        widget.project?.fixedProperty?.newarcProjectFixedAssetsPropertyPagamento = newarcProject?.fixedProperty?.newarcProjectFixedAssetsPropertyPagamento;


        widget.updateProject!(widget.project);

        /** Flag to set if the current editing row found */
        bool found = false;

        /**
         * First find the editing row in the outgoing payment section
         * If found then break and set the Paid Status
         */


        outgoingPaymentSections.forEachIndexed((index, section) {
          if (section['rows'].length > 0) {
            for (var i = 0; i < section['rows'].length; i++) {
              Map row = section['rows'][i];
              if (row['rowCounter'] == rowcounter) {
                outgoingPaymentSections[index]['rows'][i]['isPaid'] =
                isPaid[rowcounter];
                found = true;
                break;
              }
            }
          }
        });

        getProgressTotal();

        setState(() {
          savingRows[rowcounter]['message'] = 'Saved!';
        });

        Future.delayed(const Duration(milliseconds: 1500), () {
          savingRows[rowcounter]['message'] = '';
        });
      }catch(e){
        savingRows[rowcounter]['message'] = 'Si è verificato un errore.';
        print("Error while saving saveProject ---> ${e.toString()}");
      }
  }

  List<DataCell> getDataRow(Map data, {isIncomingRevenue}) {

    List<DataCell> list = [];
    try {
      int rowcounter = data['rowCounter'];

      bool isSavingRow = false;
      if (rowcounter == savingIndex) {
        isSavingRow = true;
      }


      list.add(DataCell(NarFormLabelWidget(
        label: data['title'],
        fontSize: 13,
        fontWeight: '700',
      )));


      list.add(DataCell(NarFormLabelWidget(
        label: formatPrice(double.parse(data['value'].toString()), digits: 0),
        fontSize: 13,
        fontWeight: '700',
      )));

      list.add(DataCell(statusIcon(isPaid[rowcounter])));

      list.add(DataCell((datePaidValue[rowcounter] != 0)  ? NarFormLabelWidget(
        label: getFormattedDate(datePaidValue[rowcounter]),
        fontSize: 13,
        fontWeight: '700',
      ) : SizedBox()));

     list.add(DataCell(statusIcon(data["isInvoiceUploaded"] ?? false)));

      list.add(DataCell(IconButtonWidget(
        onTap: () {
          showEditUscitePopup(data);
        },
        iconPadding: EdgeInsets.all(8),
        isSvgIcon: false,
        icon: 'assets/icons/edit.png',
        iconColor: AppColor.greyColor,
      )));

    } catch (e, s) {
      print({'ee', e, s});
    }
    
    return list;
  }

  Future<void> showAddEditEntratePaymentPopup({required NewarcProjectFixedAssetsPropertyPagamento pagamento,required EntratePayment entratePayment,required String type,required double totalAmount,required VoidCallback onRefresh,required bool isEdit}) async {

    List<String> formErrorMessage = [];

    EntratePayment newEntratePayment = entratePayment;

    TextEditingController receivedAmountController = TextEditingController();
    TextEditingController remainingAmountController = TextEditingController();

    void checkRemainingAmount(double receivedAmount){
      double remainingAmount = totalAmount - receivedAmount;
      remainingAmountController.text = localCurrencyFormatMainWithDecimal.format(remainingAmount).toString();
    }

    Rate? parentRate = pagamento.rate?.firstWhere((r) => r.uniqueId == newEntratePayment.rateUniqueId,orElse: (){return Rate.empty();});

    await showDialog(
        context: context,
        builder: (BuildContext _context) {
          receivedAmountController.text =  newEntratePayment.receivedAmount.toString();
          checkRemainingAmount(type == "saldo" ? isEdit ? totalAmount : 0 : (newEntratePayment.remainingAmount ?? 0.0));
          return StatefulBuilder(builder: (__context, _setState) {
            return Center(
                child: BaseNewarcPopup(
                  title: type == "pagamento totale" ? "${isEdit ? "Modifica":"Aggiungi"} pagamento totale" : type == "acconto" ? "${isEdit ? "Modifica":"Aggiungi"} acconto" : type == "saldo" ? "${isEdit ? "Modifica":"Aggiungi"} saldo" : "",
                  buttonText: "Salva",
                  formErrorMessage: formErrorMessage,
                  isSecondButtonVisible: true,
                  secondButtonColor: Color(0xFFEA3132),
                  secondButtonText: "Elimina",
                  onPressed: (newEntratePayment.paidAmount != null && newEntratePayment.paidAmount != 0) && (newEntratePayment.paidDate != null)  ?  () async {
                    _setState((){
                      formErrorMessage.clear();
                      formErrorMessage.add("Salvataggio in corso...");
                    });
                    try {

                      if(!isEdit){
                        newEntratePayment.uniqueId = generateRandomString(20);
                      }
                      newEntratePayment.paymentType = type;

                      if(type == "pagamento totale" || type == "saldo"){
                        newEntratePayment.paymentStatus = "pagato";
                      }else if(type == "acconto"){
                        newEntratePayment.paymentStatus = "parzialmente pagato";
                      }


                      newEntratePayment.invoicePath = newEntratePayment.isInvoiceUploaded ?? false  ?
                      {
                        'filename': newEntratePayment.invoiceImages?.isNotEmpty ?? false ? newEntratePayment.invoiceImages![0] : "",
                        'location': newEntratePayment.invoiceImages?.isNotEmpty ?? false ? "/projects/${widget.project!.id}/economics/entratePayment/${newEntratePayment.uniqueId}/" : ""
                      } : {};



                      if (parentRate?.uniqueId?.isNotEmpty ?? false) {
                        parentRate?.entratePayment ??= [];

                        final index = parentRate?.entratePayment?.indexWhere((e) => e.uniqueId == newEntratePayment.uniqueId);

                        if(type == "pagamento totale" || type == "saldo"){
                          parentRate?.paymentStatus = "pagato";
                        }else if(type == "acconto"){
                          parentRate?.paymentStatus = "parzialmente pagato";
                        }

                        double remainAmount = double.tryParse(remainingAmountController.text.replaceAll('.', '').replaceAll(',', '.').toString()) ?? 0.0;

                        newEntratePayment.remainingAmount = remainAmount;

                        if (index != -1 && isEdit) {
                          parentRate!.entratePayment![index!] = newEntratePayment;
                        } else {
                          parentRate!.entratePayment!.add(newEntratePayment);
                        }

                        double totalPaidAmount = 0;
                        for(int i =0; i < (parentRate.entratePayment?.length ?? 0); i++){
                          totalPaidAmount = totalPaidAmount + (parentRate.entratePayment?[i].paidAmount ?? 0);
                        }

                        parentRate.paidAmount = totalPaidAmount;
                      }

                      getProgressTotal();
                      setState(() {});
                     _setState((){
                       formErrorMessage.clear();
                       formErrorMessage.add("Salvato");
                     });
                      onRefresh();
                    } catch (e) {
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Si è verificato un errore.");
                      });
                      print("Error In Edit Entrate Rate ${e.toString()}");
                    }
                  } : (){
                    return false;
                  },
                  onPressedSecondButton: () async {
                    _setState((){
                      formErrorMessage.clear();
                      formErrorMessage.add("Salvataggio in corso...");
                    });
                    try {

                      Rate? parentRate = pagamento.rate?.firstWhere((r) => r.uniqueId == newEntratePayment.rateUniqueId,orElse: (){return Rate.empty();});

                      parentRate?.paymentStatus = "";


                      if (parentRate?.uniqueId?.isNotEmpty ?? false) {
                        parentRate?.entratePayment ??= [];

                        final index = parentRate?.entratePayment?.indexWhere((e) => e.uniqueId == newEntratePayment.uniqueId);



                        if (index != -1) {
                          parentRate!.entratePayment!.removeAt(index!);
                        }
                        if(parentRate?.entratePayment?.length == 0){
                          parentRate?.paymentStatus = "in attesa";
                          parentRate?.paidAmount = null;
                        }
                      }

                      getProgressTotal();
                      setState(() {});
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Salvato");
                      });
                      onRefresh();
                    } catch (e) {
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Si è verificato un errore.");
                      });
                      print("Error In Edit Entrate Rate Elimina ${e.toString()}");
                      return false;
                    }
                  },
                  buttonColor: Theme.of(context).primaryColor.withOpacity((newEntratePayment.paidAmount != null && newEntratePayment.paidAmount != 0) && (newEntratePayment.paidDate != null)  ? 1.0 : 0.5 ),
                  column: Container(
                    width: 600,
                    child: ListView(
                      shrinkWrap: true,
                      children: [
                        SizedBox(
                          width: 600,
                          height: 90,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              SizedBox(
                                width: 190,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    CustomTextFormField(
                                      label: "Somma ricevuta",
                                      isExpanded: false,
                                      isMoney: true,
                                      isShowPrefillMoneyIcon: false,
                                      labelColor: Color(0xFF696969),
                                      labelFontSize: 12,
                                      suffixIcon: Container(
                                        width: 10,
                                        padding: const EdgeInsets.only(right: 10),
                                        child: Align(
                                            alignment: Alignment.centerRight,
                                            child: NarFormLabelWidget(
                                              label: "€",
                                              textColor: AppColor.black,
                                              fontWeight: "500",
                                              fontSize: 15,
                                            )),
                                      ),
                                      controller: receivedAmountController,
                                      onChangedCallback: (String value) {
                                        double receivedAmount = double.tryParse(value.replaceAll('.', '').replaceAll(',', '.').toString()) ?? 0.0;
                                        double paidAmount = (receivedAmount * 122) / (122-(parentRate?.holdedPercentage ?? 0.0));
                                        print("paidAmount ===> ${paidAmount}");
                                        checkRemainingAmount(paidAmount);
                                        _setState((){
                                          newEntratePayment.receivedAmount = receivedAmount;
                                          newEntratePayment.paidAmount = paidAmount;
                                        });

                                      },
                                    ),
                                    SizedBox(height: 5,),
                                    NarFormLabelWidget(
                                      label: "da pagare: ${localCurrencyFormatMainWithDecimal.format(totalAmount).toString()}€",
                                      textColor:Color(0xFF787878),
                                      fontWeight: "600",
                                      fontSize: 11,
                                    )
                                  ],
                                ),
                              ),

                              SizedBox(
                                width: 190,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    NarFormLabelWidget(
                                      label: 'Data pagamento',
                                      fontSize: 12,
                                      fontWeight: '600',
                                      textColor: Color(0xFF696969),
                                    ),
                                    SizedBox(height: 13,),
                                    Row(
                                      children: [
                                        GestureDetector(
                                          onTap: () async {
                                            DateTime? _selectedDate = await showDatePicker(
                                              context: context,
                                              initialDate: newEntratePayment.paidDate != null ? DateTime.fromMillisecondsSinceEpoch(newEntratePayment.paidDate!) : DateTime.now(),
                                              firstDate: DateTime(1900),
                                              lastDate: DateTime(2100),
                                            );
                                            if (_selectedDate != null) {
                                              _setState(() {
                                                newEntratePayment.paidDate = _selectedDate.millisecondsSinceEpoch;
                                              });
                                            }
                                          },
                                          child: Container(
                                            height: 30,
                                            width: 30,
                                            decoration: BoxDecoration(
                                              color: Color(0xffeaeaea),
                                              borderRadius: BorderRadius.circular(8),
                                            ),
                                            padding: EdgeInsets.symmetric(horizontal: 5),
                                            child: Image.asset(
                                              'assets/icons/calendar.png',
                                              color: Colors.black,
                                            ),
                                          ),
                                        ),
                                        SizedBox(width: 10,),
                                        NarFormLabelWidget(
                                          label: "${(newEntratePayment.paidDate != null) ? getFormattedDate(newEntratePayment.paidDate!) : ""}",
                                          fontSize: 14,
                                          fontWeight: '600',
                                        )
                                      ],
                                    ),
                                  ],
                                ),
                              ),

                              SizedBox(
                                width: 190,
                                child: CustomTextFormField(
                                  enabled: false,
                                  readOnly: true,
                                  isExpanded: false,
                                  isMoney: true,
                                  labelColor: Color(0xFF696969),
                                  labelFontSize: 12,
                                  disabledColor: remainingAmountController.text == "0" ? Color(0xFFF3F3F3): Color(0xFFF7E2E2),
                                  controllerFontColor: remainingAmountController.text == "0" ? AppColor.black : Color(0xFFC05B5B),
                                  isShowPrefillMoneyIcon: false,
                                  label: "Rimanenza",
                                  suffixIcon: Container(
                                    width: 10,
                                    padding: const EdgeInsets.only(right: 10),
                                    child: Align(
                                        alignment: Alignment.centerRight,
                                        child: NarFormLabelWidget(
                                          label: "€",
                                          textColor: remainingAmountController.text == "0" ? AppColor.black : Color(0xFFC05B5B),
                                          fontWeight: "500",
                                          fontSize: 15,
                                        )),
                                  ),
                                  controller: remainingAmountController,
                                  onChangedCallback: (){},
                                ),
                              ),

                            ],
                          ),
                        ),
                        SizedBox(
                          height: 10,
                        ),
                        SizedBox(
                          width: 350,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Switch(
                                value: newEntratePayment.isInvoiceUploaded ?? false,
                                activeColor: AppColor.white,
                                activeTrackColor: Theme.of(context).primaryColor,
                                onChanged: (bool value) async {
                                  _setState(() {
                                    newEntratePayment.isInvoiceUploaded = value;
                                  });
                                },
                              ),
                              SizedBox(
                                width: 10,
                              ),
                              NarFormLabelWidget(
                                label: 'Carica fattura',
                                fontSize: 14,
                                fontWeight: '600',
                                textColor: AppColor.black,
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                          height: 10,
                        ),
                        Visibility(
                          visible: newEntratePayment.isInvoiceUploaded ?? false,
                          child: Container(
                            width: 350,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(13),
                              border: Border.all(
                                width: 1,
                                color: Color(0xffE1E1E1),
                              ),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(20.0),
                              child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisSize: MainAxisSize.max,
                                  children: [
                                    NarFilePickerWidget(
                                      allowMultiple: false,
                                      filesToDisplayInList: 0,
                                      removeButton: true,
                                      isDownloadable: false,
                                      allowedExtensions: ["pdf","jpeg"],
                                      removeButtonText: 'Elimina',
                                      removeButtonTextColor: Color(0xff797979),
                                      uploadButtonPosition: 'back',
                                      showMoreButtonText: '+ espandi',
                                      actionButtonPosition: 'bottom',
                                      displayFormat: 'inline-button',
                                      containerWidth: 65,
                                      containerHeight: 65,
                                      containerBorderRadius: 8,
                                      borderRadius: 7,
                                      fontSize: 11,
                                      fontWeight: '600',
                                      text: 'Carica',
                                      borderSideColor: Theme.of(context).primaryColor,
                                      hoverColor: Color.fromRGBO(133, 133, 133, 1),
                                      allFiles: newEntratePayment.invoiceImages,
                                      pageContext: context,
                                      storageDirectory: "/projects/${widget.project!.id}/economics/entratePayment/${newEntratePayment.uniqueId}/",
                                      removeExistingOnChange: true,
                                      progressMessage: [''],
                                      notAccent: true,
                                      splashColor: Color(0xffE5E5E5),
                                      height: 35,
                                      buttonWidth: 125,
                                      buttonTextColor: Colors.black,
                                      onUploadCompleted: () {
                                        _setState(() {});
                                      },
                                    ),
                                    SizedBox(
                                      height: 15,
                                    ),
                                    NarFilePickerWidget(
                                      allowMultiple: false,
                                      filesToDisplayInList: 0,
                                      removeButton: true,
                                      isDownloadable: false,
                                      allowedExtensions: ["pdf","jpeg"],
                                      removeButtonText: 'Elimina',
                                      removeButtonTextColor: Color(0xff797979),
                                      uploadButtonPosition: 'back',
                                      showMoreButtonText: '+ espandi',
                                      actionButtonPosition: 'bottom',
                                      displayFormat: 'inline-widget',
                                      containerWidth: 86,
                                      containerHeight: 86,
                                      containerBorderRadius: 8,
                                      borderRadius: 7,
                                      fontSize: 11,
                                      fontWeight: '600',
                                      text: 'Carica Icon',
                                      borderSideColor: Theme.of(context).primaryColor,
                                      hoverColor: Color.fromRGBO(133, 133, 133, 1),
                                      allFiles: newEntratePayment.invoiceImages,
                                      pageContext: context,
                                      storageDirectory: "/projects/${widget.project!.id}/economics/entratePayment/${newEntratePayment.uniqueId}/",
                                      removeExistingOnChange: true,
                                      progressMessage: [''],
                                      notAccent: true,
                                      showTitle: false,
                                      onUploadCompleted: () {
                                        _setState(() {});
                                      },
                                    ),
                                  ]),
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                ));
          });
        });
  }

  Future<void> showEditUscitePopup(data) async {

    int rowcounter = data['rowCounter'];

    List<String> formErrorMessage = [];

    TextEditingController rateController = TextEditingController();
    TextEditingController paidAmountController = TextEditingController();
    int? newPaidDate = 0;
    double newPaidAmount = data["paidAmount"] ?? 0;

    rateController.text = data['value'].toString();
    paidAmountController.text = data["paidAmount"].toString();
    newPaidDate = datePaidValue[rowcounter];
    bool newIsInvoiceUploaded = data["isInvoiceUploaded"];
    List invoiceImageList = data["invoiceImages"];

    await showDialog(
        context: context,
        builder: (BuildContext _context) {
          return StatefulBuilder(builder: (__context, _setState) {
            return Center(
                child: BaseNewarcPopup(
                  title: "Modifica voce",
                  buttonText: "Salva",
                  formErrorMessage: formErrorMessage,
                  onPressed: paidAmountController.text.trim().isNotEmpty && (newPaidDate != 0)  ?  () async {
                    _setState((){
                      formErrorMessage.clear();
                      formErrorMessage.add("Salvataggio in corso...");
                    });
                    try {

                      data["paidAmount"] = newPaidAmount;
                      data["isInvoiceUploaded"] = newIsInvoiceUploaded;
                      isPaid[rowcounter] = true;
                      datePaidValue[rowcounter] = newPaidDate!;
                      if(newIsInvoiceUploaded){
                        data["invoicePath"] = invoiceImageList.isNotEmpty ? {
                          'filename': invoiceImageList.isNotEmpty ? invoiceImageList[0] : "",
                          'location': invoiceImageList.isNotEmpty ? "/projects/${widget.project!.id}/payments/ditteProfAndMaterial/${data["uniqueId"]}/" : ""
                        } : {};
                      }

                      await saveProject(data);
                      setState(() {});
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Salvato");
                      });
                    } catch (e) {
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Si è verificato un errore.");
                      });
                      print("Error In Edit Entrate Rate ${e.toString()}");
                    }
                  } : (){
                    return false;
                  },
                  buttonColor: Theme.of(context).primaryColor.withOpacity(paidAmountController.text.trim().isNotEmpty && (newPaidDate != 0)  ? 1.0 : 0.5 ),
                  column: Container(
                    width: 600,
                    child: ListView(
                      shrinkWrap: true,
                      children: [
                        SizedBox(
                          width: 600,
                          height: 90,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              SizedBox(
                                width: 190,
                                child: CustomTextFormField(
                                  enabled: false,
                                  readOnly: true,
                                  isExpanded: false,
                                  isMoney: true,
                                  label: "Da pagare",
                                  suffixIcon: Container(
                                    width: 10,
                                    padding: const EdgeInsets.only(right: 10),
                                    child: Align(
                                        alignment: Alignment.centerRight,
                                        child: NarFormLabelWidget(
                                          label: "€",
                                          textColor: AppColor.greyColor,
                                          fontWeight: "500",
                                        )),
                                  ),
                                  controller: rateController,
                                ),
                              ),

                              SizedBox(
                                width: 190,
                                child: CustomTextFormField(
                                  label: "Pagato",
                                  isExpanded: false,
                                  suffixIcon: Container(
                                    width: 10,
                                    padding: const EdgeInsets.only(right: 10),
                                    child: Align(
                                        alignment: Alignment.centerRight,
                                        child: NarFormLabelWidget(
                                          label: "€",
                                          textColor: AppColor.greyColor,
                                          fontWeight: "500",
                                        )),
                                  ),
                                  controller: paidAmountController,
                                  onChangedCallback: (String value) {
                                    double amountToBePaid = double.tryParse(value.replaceAll('.', '').replaceAll(',', '.').toString()) ?? 0.0;
                                    _setState((){
                                      newPaidAmount = amountToBePaid;
                                    });
                                  },
                                ),
                              ),

                              SizedBox(
                                width: 190,
                                child: Row(
                                  children: [
                                    GestureDetector(
                                      onTap: () async {
                                        DateTime? _selectedDate = await showDatePicker(
                                          context: context,
                                          initialDate: newPaidDate != 0 ? DateTime.fromMillisecondsSinceEpoch(newPaidDate!) : DateTime.now(),
                                          firstDate: DateTime(1900),
                                          lastDate: DateTime(2100),
                                        );
                                        if (_selectedDate != null) {
                                          _setState(() {
                                            newPaidDate = _selectedDate.millisecondsSinceEpoch;
                                          });
                                        }
                                      },
                                      child: Container(
                                        height: 30,
                                        width: 30,
                                        decoration: BoxDecoration(
                                          color: Color(0xffeaeaea),
                                          borderRadius: BorderRadius.circular(8),
                                        ),
                                        padding: EdgeInsets.symmetric(horizontal: 5),
                                        child: Image.asset(
                                          'assets/icons/calendar.png',
                                          color: Colors.black,
                                        ),
                                      ),
                                    ),
                                    SizedBox(width: 10,),
                                    NarFormLabelWidget(
                                      label: "${(newPaidDate != 0) ? getFormattedDate(newPaidDate) : ""}",
                                      fontSize: 14,
                                      fontWeight: '600',
                                    )
                                  ],
                                ),
                              )
                            ],
                          ),
                        ),
                        SizedBox(
                          height: 10,
                        ),
                        SizedBox(
                          width: 350,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Switch(
                                value: newIsInvoiceUploaded,
                                activeColor: AppColor.white,
                                activeTrackColor: Theme.of(context).primaryColor,
                                onChanged: (bool value) async {
                                  _setState(() {
                                    newIsInvoiceUploaded = value;
                                  });
                                },
                              ),
                              SizedBox(
                                width: 10,
                              ),
                              NarFormLabelWidget(
                                label: 'Carica fattura',
                                fontSize: 14,
                                fontWeight: '600',
                                textColor: AppColor.black,
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                          height: 10,
                        ),
                        Visibility(
                          visible: newIsInvoiceUploaded,
                          child: Container(
                            width: 350,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(13),
                              border: Border.all(
                                width: 1,
                                color: Color(0xffE1E1E1),
                              ),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(20.0),
                              child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisSize: MainAxisSize.max,
                                  children: [
                                    NarFilePickerWidget(
                                      allowMultiple: false,
                                      filesToDisplayInList: 0,
                                      removeButton: true,
                                      isDownloadable: false,
                                      allowedExtensions: ["pdf","jpeg"],
                                      removeButtonText: 'Elimina',
                                      removeButtonTextColor: Color(0xff797979),
                                      uploadButtonPosition: 'back',
                                      showMoreButtonText: '+ espandi',
                                      actionButtonPosition: 'bottom',
                                      displayFormat: 'inline-button',
                                      containerWidth: 65,
                                      containerHeight: 65,
                                      containerBorderRadius: 8,
                                      borderRadius: 7,
                                      fontSize: 11,
                                      fontWeight: '600',
                                      text: 'Carica',
                                      borderSideColor: Theme.of(context).primaryColor,
                                      hoverColor: Color.fromRGBO(133, 133, 133, 1),
                                      allFiles: invoiceImageList,
                                      pageContext: context,
                                      storageDirectory: "/projects/${widget.project!.id}/payments/ditteProfAndMaterial/${data["uniqueId"]}/",
                                      removeExistingOnChange: true,
                                      progressMessage: [''],
                                      notAccent: true,
                                      splashColor: Color(0xffE5E5E5),
                                      height: 35,
                                      buttonWidth: 125,
                                      buttonTextColor: Colors.black,
                                      onUploadCompleted: () {
                                        _setState(() {});
                                      },
                                    ),
                                    SizedBox(
                                      height: 15,
                                    ),
                                    NarFilePickerWidget(
                                      allowMultiple: false,
                                      filesToDisplayInList: 0,
                                      removeButton: true,
                                      isDownloadable: false,
                                      allowedExtensions: ["pdf","jpeg"],
                                      removeButtonText: 'Elimina',
                                      removeButtonTextColor: Color(0xff797979),
                                      uploadButtonPosition: 'back',
                                      showMoreButtonText: '+ espandi',
                                      actionButtonPosition: 'bottom',
                                      displayFormat: 'inline-widget',
                                      containerWidth: 86,
                                      containerHeight: 86,
                                      containerBorderRadius: 8,
                                      borderRadius: 7,
                                      fontSize: 11,
                                      fontWeight: '600',
                                      text: 'Carica Icon',
                                      borderSideColor: Theme.of(context).primaryColor,
                                      hoverColor: Color.fromRGBO(133, 133, 133, 1),
                                      allFiles: invoiceImageList,
                                      pageContext: context,
                                      storageDirectory: "/projects/${widget.project!.id}/payments/ditteProfAndMaterial/${data["uniqueId"]}/",
                                      removeExistingOnChange: true,
                                      progressMessage: [''],
                                      notAccent: true,
                                      showTitle: false,
                                      onUploadCompleted: () {
                                        _setState(() {});
                                      },
                                    ),
                                  ]),
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                ));
          });
        });
  }

  Future<void> showAddEditPagamentoPopup(NewarcProjectFixedAssetsPropertyPagamento pagamento,Rate rate) async {

    List<String> formErrorMessage = [];

    Rate newRate = rate;

    TextEditingController holdedPercentageController = TextEditingController();

    await showDialog(
        context: context,
        builder: (BuildContext _context) {
          double totalRate = (newRate.rate ?? 0) + (newRate.ivaAmount ?? 0);

          if ((newRate.margeRateUniqueIDS?.isNotEmpty ?? false)) {
            for (String mergedId in newRate.margeRateUniqueIDS!) {
              final mergedRate = findRateById(mergedId);
              if (mergedRate != null) {
                totalRate += ((mergedRate.rate ?? 0) + (mergedRate.ivaAmount ?? 0));
              }
            }
          }

          bool isSaldoBtnActive = false;

          void checkRemainAmount(){
            for(int i=0;i < (newRate.entratePayment?.length ?? 0);i++){
              if(newRate.entratePayment?[i].paymentType == "acconto"){
                isSaldoBtnActive = true;
              }
            }
            newRate.toBePaidAmount = totalRate;
          }

          holdedPercentageController.text = newRate.holdedPercentage.toString();

          return StatefulBuilder(builder: (__context, _setState) {
            onRefresh(){
              rate.entratePayment = newRate.entratePayment;
              checkRemainAmount();
              _setState((){});
            }

            checkRemainAmount();
            return Center(
                child: BaseNewarcPopup(
                  title: "Modifica pagamento",
                  buttonText: "Salva",
                  formErrorMessage: formErrorMessage,
                  onPressed:  () async {
                    _setState((){
                      formErrorMessage.clear();
                      formErrorMessage.add("Salvataggio in corso...");
                    });
                    try {
                      final FirebaseFirestore _db = FirebaseFirestore.instance;
                      await _db
                          .collection(appConfig.COLLECT_NEWARC_PROJECTS)
                          .doc(widget.project!.id)
                          .update({'fixedProperty.newarcProjectFixedAssetsPropertyPagamento':
                        widget.project!.fixedProperty!.newarcProjectFixedAssetsPropertyPagamento
                            ?.map((e) => e.toMap()).toList(),
                      });
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Salvato");
                      });

                    } catch (e) {
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Si è verificato un errore.");
                      });
                      print("Error In Edit Entrate Rate ${e.toString()}");
                    }
                  },
                  buttonColor: Theme.of(context).primaryColor,
                  column: Container(
                    width: MediaQuery.of(context).size.width * 0.6,
                    height: MediaQuery.of(context).size.height * 0.8,
                    child: ListView(
                      shrinkWrap: true,
                      children: [
                        Center(
                          child: NarFormLabelWidget(
                            label: pagamento.categoryName,// category name
                            fontSize: 14,
                            fontWeight: '700',
                          ),
                        ),
                        SizedBox(height: 5,),
                        Center(
                          child: NarFormLabelWidget(
                            label: "Rata${rate.index} - ${rate.description}",
                            fontSize: 14,
                            fontWeight: '500',
                          ),
                        ),
                        SizedBox(height: 22,),

                        Container(
                          width: MediaQuery.of(context).size.width * 0.6,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  NarFormLabelWidget(
                                    label: "Da pagare",
                                    fontSize: 16,
                                    fontWeight: '700',
                                  ),
                                  SizedBox(height: 8,),
                                  Container(
                                    height: 105,
                                    width: MediaQuery.of(context).size.width * 0.1,
                                    alignment: Alignment.center,
                                    padding: EdgeInsets.symmetric(horizontal: 10),
                                    decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(7),
                                        border: Border.all(width: 1,color: Color(0xFFDADADA))
                                    ),
                                    child: NarFormLabelWidget(
                                      label: "${localCurrencyFormatMainWithDecimal.format(newRate.toBePaidAmount ?? 0).toString()}€",
                                      fontSize: 15,
                                      fontWeight: '700',
                                    ),
                                  ),
                                ],
                              ),

                              SizedBox(width: 25,),

                              Visibility(
                                visible: pagamento.hasConcessions!,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    NarFormLabelWidget(
                                      label: "Calcola accredito",
                                      fontSize: 16,
                                      fontWeight: '700',
                                    ),
                                    SizedBox(height: 8,),
                                    Container(
                                      height: 105,
                                      width: MediaQuery.of(context).size.width * 0.48,
                                      alignment: Alignment.center,
                                      decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(7),
                                          border: Border.all(width: 1,color: Color(0xFFDADADA))
                                      ),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        children: [
                                          Expanded(
                                            child: Container(
                                              // width: 130,
                                              padding: const EdgeInsets.only(top: 15),
                                              margin: const EdgeInsets.only(left: 10),
                                              child: Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  NarFormLabelWidget(
                                                    label: "Percentuale ritenuta",
                                                    fontSize: 12,
                                                    fontWeight: '600',
                                                  ),
                                                  SizedBox(height: 10,),
                                                  CustomTextFormField(
                                                    label: "",
                                                    isExpanded: false,
                                                    isNumber: true,
                                                    suffixIcon: Container(
                                                      alignment: Alignment.center,
                                                      width: 35,
                                                      child: NarFormLabelWidget(
                                                        label: "%",
                                                        fontSize: 14,
                                                        fontWeight: '500',
                                                      ),
                                                    ),
                                                    controller: holdedPercentageController,
                                                    onChangedCallback: (value){
                                                      if(value != null){
                                                        int holdedPercentage = int.tryParse(value) ?? 0;
                                                        double toBePaid = newRate.toBePaidAmount ?? 0;

                                                        double baseAmount = toBePaid / 1.22;
                                                        double holdedAmount = baseAmount * (holdedPercentage / 100);

                                                        newRate.holdedPercentage = holdedPercentage;
                                                        newRate.holdedAmount = holdedAmount;
                                                        newRate.expectedAmount = toBePaid - holdedAmount;

                                                        _setState((){});
                                                      }
                                                    },
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                          Expanded(
                                            child: Container(
                                              padding: const EdgeInsets.only(top: 15),
                                              child: Column(
                                                crossAxisAlignment: CrossAxisAlignment.center,
                                                children: [
                                                  Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      NarFormLabelWidget(
                                                        label: "Ritenuta",
                                                        fontSize: 12,
                                                        fontWeight: '600',
                                                      ),
                                                      SizedBox(height: 25,),
                                                      NarFormLabelWidget(
                                                        label: "${localCurrencyFormatMainWithDecimal.format(newRate.holdedAmount ?? 0)}€",
                                                        fontSize: 14,
                                                        fontWeight: '600',
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                          Expanded(
                                            child: Container(
                                              height: 104,
                                              decoration: BoxDecoration(
                                                color: Theme.of(context).primaryColor,
                                                borderRadius: BorderRadius.circular(7),
                                              ),
                                              padding: EdgeInsets.symmetric(horizontal: 10,vertical: 10),
                                              child: Column(
                                                children: [
                                                  NarFormLabelWidget(
                                                    label: "Accredito atteso",
                                                    fontSize: 12,
                                                    fontWeight: '600',
                                                    textColor: Colors.white,
                                                  ),
                                                  SizedBox(height: 10,),
                                                  Container(
                                                    height: 45,
                                                    padding: EdgeInsets.symmetric(horizontal: 20,vertical: 10),
                                                    decoration: BoxDecoration(
                                                      color: Colors.white,
                                                      borderRadius: BorderRadius.circular(7),
                                                    ),
                                                    child: Row(
                                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                      crossAxisAlignment: CrossAxisAlignment.center,
                                                      children: [
                                                        NarFormLabelWidget(
                                                          label: "${localCurrencyFormatMainWithDecimal.format(newRate.expectedAmount ?? 0)}",
                                                          fontSize: 15,
                                                          fontWeight: '700',
                                                        ),
                                                        NarFormLabelWidget(
                                                          label: "€",
                                                          fontSize: 15,
                                                          fontWeight: '600',
                                                        ),
                                                      ],
                                                    ),
                                                  )
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                            ],
                          ),
                        ),
                        SizedBox(height: 38,),

                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            NarFormLabelWidget(
                              label: "Pagamenti ricevuti",
                              fontSize: 17,
                              fontWeight: '700',
                            ),
                            Row(
                              children: [
                                IconTextButtonWidget(
                                  icon: "assets/icons/plus.svg",
                                  onPressed: (){
                                    EntratePayment _entratePayment = EntratePayment.empty();
                                    _entratePayment.rateUniqueId = rate.uniqueId;
                                    showAddEditEntratePaymentPopup(pagamento: pagamento,entratePayment: _entratePayment,totalAmount: totalRate,type: "pagamento totale",onRefresh: onRefresh,isEdit: false);
                                  },
                                  iconColor: AppColor.white,
                                  text: "Pagamento totale",
                                  iconOnLeft: true,
                                  iconHeight: 11,
                                  height: 32,
                                  backgroundColor: Theme.of(context).primaryColor,
                                  textStyle: TextStyle(
                                    fontSize: 13,
                                    fontFamily: "Raleway-500",
                                    color: AppColor.white
                                  ),
                                ),
                                SizedBox(width: 12,),
                                IconTextButtonWidget(
                                  icon: "assets/icons/plus.svg",
                                  onPressed: (){
                                    List<double>? _entratePaymentAmountList = rate.entratePayment?.where((item){
                                      return item.paymentType == "acconto";
                                    }).map((val){
                                     return val.paidAmount ?? 0;
                                    }).toList();
                                    double totalPayment = 0;
                                    for(int i=0;i<(_entratePaymentAmountList?.length ?? 0);i++){
                                      totalPayment = totalPayment + _entratePaymentAmountList![i];
                                    }

                                    double _newTotalRate = totalPayment !=0 ? (totalRate - totalPayment) : totalRate;
                                    EntratePayment _entratePayment = EntratePayment.empty();
                                    _entratePayment.rateUniqueId = rate.uniqueId;
                                    showAddEditEntratePaymentPopup(pagamento: pagamento,entratePayment: _entratePayment,totalAmount : _newTotalRate,type: "acconto",onRefresh: onRefresh,isEdit: false);
                                  },
                                  iconColor: Theme.of(context).primaryColor ,
                                  text: "Acconto",
                                  iconOnLeft: true,
                                  iconHeight: 11,
                                  height: 32,
                                  backgroundColor: Color(0xFFE8F9F2),
                                  textStyle: TextStyle(
                                      fontSize: 13,
                                      fontFamily: "Raleway-500",
                                      color: Theme.of(context).primaryColor
                                  ),
                                ),
                                SizedBox(width: 12,),
                                IconTextButtonWidget(
                                  icon: "assets/icons/plus.svg",
                                  onPressed: isSaldoBtnActive ? (){
                                    EntratePayment _entratePayment = EntratePayment.empty();
                                    _entratePayment.rateUniqueId = rate.uniqueId;
                                    _entratePayment.remainingAmount = rate.entratePayment?.isNotEmpty ?? false ? rate.entratePayment?.first.remainingAmount : 0.0;
                                    double totalPayment = 0;
                                    List<double>? _entratePaymentAmountList = rate.entratePayment?.where((item){
                                      return item.paymentType == "acconto";
                                    }).map((val){
                                      return val.paidAmount ?? 0;
                                    }).toList();
                                    for(int i=0;i<(_entratePaymentAmountList?.length ?? 0);i++){
                                      totalPayment = totalPayment + _entratePaymentAmountList![i];
                                    }

                                    double _newTotalRate = totalPayment !=0 ? (totalRate - totalPayment) : totalRate;
                                    showAddEditEntratePaymentPopup(pagamento: pagamento,entratePayment: _entratePayment, totalAmount: _newTotalRate,type: "saldo",onRefresh: onRefresh,isEdit: false);
                                  } : (){},
                                  iconColor: Theme.of(context).primaryColor.withOpacity(isSaldoBtnActive ? 1.0 : 0.5),
                                  text: "Saldo",
                                  iconOnLeft: true,
                                  iconHeight: 11,
                                  height: 32,
                                  backgroundColor: Color(0xFFE8F9F2).withOpacity(isSaldoBtnActive ? 1.0 : 0.5),
                                  textStyle: TextStyle(
                                      fontSize: 13,
                                      fontFamily: "Raleway-500",
                                      color: Theme.of(context).primaryColor.withOpacity(isSaldoBtnActive ? 1.0 : 0.5)
                                  ),
                                ),
                              ],
                            )
                          ],
                        ),
                        SizedBox(height: 30,),

                        ListView(
                          shrinkWrap: true,
                          children: [
                            ...rate.entratePayment!.map((entrate) {
                              return Container(
                                margin: EdgeInsets.only(top: 15),
                                child: Stack(
                                  clipBehavior: Clip.none,
                                  children: [
                                    Container(
                                      width: MediaQuery.of(context).size.width * 0.6,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(7),
                                        border: Border.all(width: 1, color: Color(0xFFDADADA)),
                                      ),
                                      padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                                      child: Row(
                                        crossAxisAlignment: CrossAxisAlignment.center,
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          SizedBox(
                                            width: 190,
                                            child: CustomTextFormField(
                                              enabled: false,
                                              readOnly: true,
                                              isExpanded: false,
                                              isMoney: true,
                                              isReadOnlyBoarder: false,
                                              isShowPrefillMoneyIcon: false,
                                              controllerFontColor: AppColor.black,
                                              hintTextColor: AppColor.black,
                                              controllerFontSize: 14,
                                              disabledColor: Color(0xFFF1F1F1),
                                              label: "Somma ricevuta",
                                              labelColor: Color(0xFF696969),
                                              suffixIcon: Container(
                                                width: 10,
                                                padding: EdgeInsets.only(right: 10),
                                                child: Align(
                                                  alignment: Alignment.centerRight,
                                                  child: NarFormLabelWidget(
                                                    label: "€",
                                                    textColor: AppColor.greyColor,
                                                    fontWeight: "500",
                                                  ),
                                                ),
                                              ),
                                              controller: TextEditingController(text: entrate.receivedAmount != null ? localCurrencyFormatMainWithDecimal.format(entrate.receivedAmount).toString() : ""),
                                            ),
                                          ),
                                          SizedBox(
                                            width: 190,
                                            child: CustomTextFormField(
                                              enabled: false,
                                              readOnly: true,
                                              isExpanded: false,
                                              isMoney: true,
                                              isReadOnlyBoarder: false,
                                              isShowPrefillMoneyIcon: false,
                                              controllerFontColor: AppColor.black,
                                              hintTextColor: AppColor.black,
                                              controllerFontSize: 14,
                                              disabledColor: Color(0xFFF1F1F1),
                                              label: "Somma bonificata",
                                              labelColor: Color(0xFF696969),
                                              suffixIcon: Container(
                                                width: 10,
                                                padding: EdgeInsets.only(right: 10),
                                                child: Align(
                                                  alignment: Alignment.centerRight,
                                                  child: NarFormLabelWidget(
                                                    label: "€",
                                                    textColor: AppColor.greyColor,
                                                    fontWeight: "500",
                                                  ),
                                                ),
                                              ),
                                              controller: TextEditingController(text: entrate.paidAmount != null ? localCurrencyFormatMainWithDecimal.format(entrate.paidAmount).toString() : ""),
                                            ),
                                          ),
                                          SizedBox(
                                            width: 190,
                                            child: CustomTextFormField(
                                              enabled: false,
                                              readOnly: true,
                                              isExpanded: false,
                                              isMoney: true,
                                              isShowPrefillMoneyIcon: false,
                                              label: "Rimanenza",
                                              hintTextColor: AppColor.black,
                                              labelColor: Color(0xFF696969),
                                              disabledColor: entrate.remainingAmount == null || entrate.remainingAmount == 0 ? Color(0xFFE8F9F2) : Color(0xFFF7E2E2),
                                              controllerFontColor: AppColor.black,
                                              controllerFontSize: 14,
                                              labelFontSize: 12,
                                              suffixIcon: Container(
                                                width: 10,
                                                padding: EdgeInsets.only(right: 10),
                                                child: Align(
                                                  alignment: Alignment.centerRight,
                                                  child: NarFormLabelWidget(
                                                    label: "€",
                                                    textColor: AppColor.black,
                                                    fontWeight: "500",
                                                    fontSize: 15,
                                                  ),
                                                ),
                                              ),
                                              controller: TextEditingController(text: localCurrencyFormatMainWithDecimal.format(entrate.remainingAmount ?? 0.0).toString()),
                                            ),
                                          ),
                                          Column(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              NarFormLabelWidget(
                                                label: "Data pagamento",
                                                fontSize: 12,
                                                fontWeight: '600',
                                                textColor: Color(0xFF696969),
                                              ),
                                              SizedBox(height: 19),
                                              NarFormLabelWidget(
                                                label: "${(entrate.paidDate != null) ? getFormattedDate(entrate.paidDate!) : ""}",
                                                fontSize: 14,
                                                fontWeight: '600',
                                              ),
                                            ],
                                          ),
                                          Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              NarFormLabelWidget(
                                                label: "Fattura",
                                                fontSize: 12,
                                                fontWeight: '600',
                                                textColor: Color(0xFF696969),
                                              ),
                                              SizedBox(height: 19),
                                              SizedBox(
                                                width: 120,
                                                child: NarFormLabelWidget(
                                                  label: entrate.invoiceImages?.isNotEmpty ?? false ? entrate.invoiceImages![0] : "",
                                                  fontSize: 14,
                                                  fontWeight: '600',
                                                  textDecoration: TextDecoration.underline,
                                                  overflow: TextOverflow.ellipsis,
                                                ),
                                              ),
                                            ],
                                          ),
                                          IconButtonWidget(
                                            onTap: () {
                                              double totalPaidRate = 0;

                                              for(int i=0;i < (newRate.entratePayment?.length ?? 0);i++){
                                                totalPaidRate += newRate.entratePayment?[i].remainingAmount ?? 0;
                                              }
                                              showAddEditEntratePaymentPopup(pagamento: pagamento,entratePayment: entrate,totalAmount: entrate.paymentType == "saldo" ? totalPaidRate : totalRate,type: entrate.paymentType ?? "",onRefresh: onRefresh,isEdit: true);
                                            },
                                            isSvgIcon: true,
                                            icon: 'assets/icons/edit.svg',
                                            backgroundColor: Color(0xFF4D4D4D),
                                            iconColor: AppColor.white,
                                            borderRadius: 7,
                                            height: 27,
                                            width: 27,
                                          ),
                                        ],
                                      ),
                                    ),
                                    Align(
                                      alignment: Alignment.topCenter,
                                      child: Transform.translate(
                                        offset: Offset(0, -7.5),
                                        child: Container(
                                          height: 15,
                                          width: 120,
                                          alignment: Alignment.center,
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(7.5),
                                            color: entrate.paymentType == "acconto" ? Color(0xFFC9E7DA) : Theme.of(context).primaryColor,
                                          ),
                                          child: NarFormLabelWidget(
                                            label: entrate.paymentType?.toCapitalized(),
                                            fontSize: 11,
                                            fontWeight: '700',
                                            textColor: entrate.paymentType == "acconto" ? Theme.of(context).primaryColor : AppColor.white,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            }).toList(),
                          ],
                        )
                      ],
                    ),
                  ),
                ));
          });
        });
  }

}


