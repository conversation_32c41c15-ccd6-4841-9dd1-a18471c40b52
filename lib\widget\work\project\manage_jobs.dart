import 'dart:developer';

import 'package:flutter_svg/flutter_svg.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/classes/newarcProject.dart';
import 'package:newarc_platform/classes/notificationRenovationApp.dart';
import 'package:newarc_platform/classes/process.dart';
import 'package:newarc_platform/classes/projectJob.dart';
import 'package:newarc_platform/classes/supplier.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/functions/various.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/color-bg-dropdown.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/file-picker.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/image-select-box.dart';
import 'package:newarc_platform/widget/UI/link.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/widget/work/project/newarc_gantt.dart';
import 'package:intl/intl.dart';

class ProjectManageJobs extends StatefulWidget {
  final NewarcProject? project;
  final List<Supplier>? suppliers;
  final Function? updateProject;

  const ProjectManageJobs(
      {Key? key, this.project, this.updateProject, this.suppliers})
      : super(key: key);

  @override
  State<ProjectManageJobs> createState() => _ProjectManageJobsState();
}

class _ProjectManageJobsState extends State<ProjectManageJobs> {
  NumberFormat localCurrencyFormat =
      NumberFormat.currency(locale: 'it_IT', symbol: '\€', decimalDigits: 0);
  NumberFormat localCurrencyFormatMain =
      NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 0);

  TextEditingController contJobStart = new TextEditingController();
  TextEditingController contJobEnd = new TextEditingController();
  TextEditingController contHypotheticalJobEndDate =
      new TextEditingController();

  int? jobStart;
  int? jobEnd;
  int? hypotheticalJobEndDate;

  TextEditingController contIndexPlace = new TextEditingController();
  TextEditingController contComment = new TextEditingController();
  TextEditingController contActivity = new TextEditingController();
  TextEditingController contCategory = new TextEditingController();
  TextEditingController contTimelineMain = new TextEditingController();
  TextEditingController contVendor = new TextEditingController();

  List<TextEditingController> contStatus = [];
  List<TextEditingController> contQuality = [];
  List<int> jobDateStartOn = [];
  List<int> jobDateEndOn = [];

  List<Process> process = [];
  Supplier? selectedVendor;

  List<Map> vendors = [];
  List<String> timelineOptions = List.generate(30, (i) => i.toString() );
  bool? hasSingleVendor;
  bool? hasVendor;

  List<bool> isAnimated = [];
  List<bool> isCommentAnimated = [];
  List<bool> isImageAnimated = [];
  final List<List> allFiles = [];

  int keepOpenCommentBoxIndex = -1;
  int keepOpenImageBoxIndex = -1;

  String? tmpTitle;
  bool? showTitle;

  List<String> fileProgressMessage = [''];
  List selectedJobs = [];

  List<Map> quality = [
    {
      'value': '',
      'label': '',
      'bgColor': Color(0xffA4A4A4),
      'textColor': Colors.white,
    },
    {
      'value': 'Da verificare',
      'label': 'Da verificare',
      'bgColor': Color(0xffE4F0EB),
      'textColor': Color(0xff489B79),
      'borderColor': Color(0xff489B79),
    },
    {
      'value': 'Verificata',
      'label': 'Verificata',
      'bgColor': Color(0xff489B79),
      'textColor': Colors.white,
      'borderColor': Color(0xff489B79),
    }
  ];

  List<Map> statusDelivery = [
    {
      'value': '3 Da fare',
      'label': 'Da fare',
      'bgColor': Color(0xffBEBEBE),
      'textColor': Colors.white
    },
    {
      'value': '1 In corso',
      'label': 'In corso',
      'bgColor': Color(0xffFF8800),
      'textColor': Colors.white
    },
    {
      'value': '2 Completati',
      'label': 'Completati',
      'bgColor': Color(0xff39C14F),
      'textColor': Colors.white
    }
  ];

  /*
  In corso
  Completati
  */

  List<Map> _activity = [];
  List<Map> activity = [];

  String progressMessage = '';
  List<Map<String, String>> uniqueCategoryList = [];

  List<JobEventItem> jobEventItemList = [];
  Map<String, List<JobEventItem>> jobEventItemSuppliersMapList = {};

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      fetchCategoryActivities();
    });
    setInitialValues();

    print({'vendorAndProfessionals', widget.project!.vendorAndProfessionals!.map((e) => e.toMap())});
  }

  fetchCategoryActivities() async {

    _activity.clear();
    try {
      final FirebaseFirestore _db = FirebaseFirestore.instance;
      QuerySnapshot<Map<String, dynamic>> snapshot = await _db
          .collection(appConfig.COLLECT_CATEGORY_ACTIVITY)
          .get();

      if( snapshot.docs.length > 0 ) {
        
        _activity.add({'value': '', 'label': '', 'timeline': 0, 'category': ''});
        
        for (var i = 0; i < snapshot.docs.length; i++) {
          // List<Map<String, dynamic>> existingActivities = 

          if( snapshot.docs[i].data()['activities'] != null ) {
            (snapshot.docs[i].data()['activities'] as List<dynamic>?)
              ?.map((acti) {
                Map<String, dynamic>.from(acti);
                _activity.add({
                  'value': acti['activity'],
                  'label': acti['activity'],
                  'timeline': acti['timeline'],
                  'category': snapshot.docs[i].data()['categoryName'],
                });    
              }).toList();    
          } else {
            _activity.add({
              'value': '',
              'label': '',
              'timeline': 0,
              'category': snapshot.docs[i].data()['categoryName'],
            });    
          }
        }

        Set<String> uniqueCategories = {};

        // Iterate through the activity list and add each category to the set
        for (var item in _activity) {
          if (item.containsKey('category')) {
            uniqueCategories.add(item['category']);
          }
        }

        // Convert the set to a list of maps
        uniqueCategoryList = uniqueCategories
            .map((category) => {'value': category, 'label': category})
            .toList();
        
      }
    } catch (e,s) {
      print({'fetchCategoryActivities',e,s});
    }

  }

  @protected
  void didUpdateWidget(ProjectManageJobs oldWidget) {
    super.didUpdateWidget(oldWidget);

    setInitialValues();
  }

  setInitialValues() {
    isCommentAnimated.clear();
    isImageAnimated.clear();
    isAnimated.clear();
    contStatus.clear();
    contQuality.clear();
    jobDateStartOn.clear();
    jobDateEndOn.clear();
    allFiles.clear();

    tmpTitle = '';
    showTitle = true;

    final manualOrder = {
      '2 Completati': 0,
      '1 In corso': 1,
      '3 Da fare': 2,
    };

    widget.project!.projectJobs!.sort((a, b) {
      final aOrder = manualOrder[a.status!] ?? 999;
      final bOrder = manualOrder[b.status!] ?? 999;

      if (aOrder != bOrder) {
        return aOrder.compareTo(bOrder);
      }

      // group: by endDate descending for 2 Completati
      if (a.status == '2 Completati' && b.status == '2 Completati') {
        final aDate = DateTime.fromMillisecondsSinceEpoch(a.endDate ?? 0);
        final bDate = DateTime.fromMillisecondsSinceEpoch(b.endDate ?? 0);
        return  aDate.compareTo(bDate); // descending
      }

      return 0;
    });

    if (widget.project!.projectJobs!.length > 0) {
      for (var i = 0; i < widget.project!.projectJobs!.length; i++) {
        widget.project!.projectJobs![i].indexPlace = i;



        contStatus.add(new TextEditingController());
        contQuality.add(new TextEditingController());

        contStatus[i].text = widget.project!.projectJobs![i].status!;
        contQuality[i].text = widget.project!.projectJobs![i].quality!;

        jobDateStartOn.add(widget.project!.projectJobs![i].startDate!);
        jobDateEndOn.add(widget.project!.projectJobs![i].endDate!);

        isCommentAnimated.add(false);
        isImageAnimated.add(false);
        isAnimated.add(false);
        allFiles.add(widget.project!.projectJobs![i].images!);
      }
    }

    if (widget.project!.jobStartDate != null &&
        widget.project!.jobStartDate! > 0) {
      jobStart = widget.project!.jobStartDate;
      DateTime startOn = DateTime.fromMillisecondsSinceEpoch(jobStart!);
      String _jobStartDate = (startOn.day > 9
              ? startOn.day.toString()
              : '0' + startOn.day.toString()) +
          '/' +
          (startOn.month > 9
              ? startOn.month.toString()
              : '0' + startOn.month.toString()) +
          '/' +
          startOn.year.toString();

      contJobStart.text = _jobStartDate;
    }

    if (widget.project!.jobEndDate != null && widget.project!.jobEndDate! > 0) {
      jobEnd = widget.project!.jobEndDate;
      DateTime endOn = DateTime.fromMillisecondsSinceEpoch(jobEnd!);
      String _jobEndDate =
          (endOn.day > 9 ? endOn.day.toString() : '0' + endOn.day.toString()) +
              '/' +
              (endOn.month > 9
                  ? endOn.month.toString()
                  : '0' + endOn.month.toString()) +
              '/' +
              endOn.year.toString();
      contJobEnd.text = _jobEndDate;
    }

    if (widget.project!.hypotheticalJobEndDate != null &&
        widget.project!.hypotheticalJobEndDate! > 0) {
      hypotheticalJobEndDate = widget.project!.hypotheticalJobEndDate;
      DateTime hypotheticalJobEndDateOn =
          DateTime.fromMillisecondsSinceEpoch(hypotheticalJobEndDate!);
      String _hypotheticalJobEndDateOn = (hypotheticalJobEndDateOn.day > 9
              ? hypotheticalJobEndDateOn.day.toString()
              : '0' + hypotheticalJobEndDateOn.day.toString()) +
          '/' +
          (hypotheticalJobEndDateOn.month > 9
              ? hypotheticalJobEndDateOn.month.toString()
              : '0' + hypotheticalJobEndDateOn.month.toString()) +
          '/' +
          hypotheticalJobEndDateOn.year.toString();
      contHypotheticalJobEndDate.text = _hypotheticalJobEndDateOn;
    }

    // if (keepOpenCommentBoxIndex != -1) {
    //   isCommentAnimated[keepOpenCommentBoxIndex] = true;
    //   isAnimated[keepOpenCommentBoxIndex] = true;
    // }

    // if (keepOpenImageBoxIndex > -1) {
    //   isImageAnimated[keepOpenImageBoxIndex] = true;
    // }

    if (keepOpenCommentBoxIndex > -1) {
      isAnimated[keepOpenCommentBoxIndex] = true;
    }

    hasVendor = false;

    jobEventItemList = (widget.project?.projectJobs?.isNotEmpty ?? false)
        ? widget.project!.projectJobs!.toJobEventItems()
        : [];

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: double.infinity,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.max,
        children: [
          Expanded(
            child: ListView(
              children: [
                SizedBox(height: 20),
                Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      flex: 1,
                      child: NarFormLabelWidget(
                        label: 'Gestisci Lavori',
                        fontSize: 20,
                        fontWeight: 'bold',
                      ),
                    ),
                    BaseNewarcButton(
                      onPressed: () {
                        addJobPopup(context, null);
                      },
                      buttonText: 'Aggiungi Lavorazione',
                    )
                  ],
                ),
                SizedBox(height: 30),
                Container(
                  // color: Colors.grey,
                  // width: 200,
                  child: ListView(
                    shrinkWrap: true,
                    children: [
                      Column(
                        children: widget.project!.projectJobs!.map((e) {
                          return jobWrapper(context, e);
                        }).toList(),
                      ),
                      
                    ],
                  ),
                )
              ],
            ),
          ),
          SizedBox(height: 10,),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  NarFormLabelWidget(
                    label: 'Fine lavori ipotetica',
                    fontSize: 13,
                    fontWeight: '600',
                    textColor: AppColor.greyColor,
                  ),
                  SizedBox(width: 10,),
                  MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                      onTap: ()async{
                        final selectedDate = await showDatePicker(
                          context: context,
                          initialDate: (widget.project?.hypotheticalJobEndDate != null && widget.project?.hypotheticalJobEndDate != 0) ? DateTime.fromMillisecondsSinceEpoch(widget.project?.hypotheticalJobEndDate ?? 0) :  DateTime.now(),
                          firstDate: DateTime(1900),
                          lastDate: DateTime(2100),
                        );
                        if (selectedDate != null) {

                          setState(() {
                            contHypotheticalJobEndDate.text = getFormattedDate(selectedDate.millisecondsSinceEpoch);
                            widget.project?.hypotheticalJobEndDate = selectedDate.millisecondsSinceEpoch;
                          });
                          await saveProject();
                        }
                      },
                      child: Container(
                        width: 145,
                        height: 45,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(7),
                            border: Border.all(color: Color(0xFFDBDBDB))
                        ),
                        padding: EdgeInsets.symmetric(vertical: 8,horizontal: 10),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            NarFormLabelWidget(
                              label: (contHypotheticalJobEndDate.text.isNotEmpty && contHypotheticalJobEndDate.text != "0")   ? contHypotheticalJobEndDate.text : "",
                              fontSize: 13,
                              fontWeight: '600',
                              textColor: AppColor.black,
                            ),
                            SvgPicture .asset(
                              "assets/icons/calendar.svg",
                              color: Color(0xff7B7B7B),
                              height: 17,
                              width: 17,
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              BaseNewarcButton(
                color: Color(0xffE0E0E0),
                textColor: Color(0xff4B4B4B),
                buttonText: "Visualizza Cronoprogramma",
                fontWeight: '600',
                fontSize: 15,
                height: 40,
                width: 260,
                onPressed: () async {
                  _showActivitiesGanttDialog(context);
                },
              ),
            ],
          )
        ],
      ),
    );
  }


  void _showActivitiesGanttDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Container(
            width: MediaQuery.of(context).size.width,
            padding: const EdgeInsets.all(16),
            child: NewarcGantt(
              name: widget.project!.name,
              rows: jobEventItemList,
              monthNames: italianMonths,
              columnWidth: 375,
              labelColumnWidth: 500,
              barHeight: 25,
            )
          ),
        );
      },
    );
  }

  void _showSuppliersGanttDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Container(
            width: MediaQuery.of(context).size.width,
            padding: const EdgeInsets.all(16),
            child: NewarcGantt(
              name: contActivity.text,
              rows: jobEventItemSuppliersMapList,
              monthNames: italianMonths,
              columnWidth: 375,
              labelColumnWidth: 250,
              barHeight: 25,
            )
          ),
        );
      },
    );
  }

  final Map<int, String> italianMonths = {
    1: "gennaio",
    2: "febbraio",
    3: "marzo",
    4: "aprile",
    5: "maggio",
    6: "giugno",
    7: "luglio",
    8: "agosto",
    9: "settembre",
    10: "ottobre",
    11: "novembre",
    12: "dicembre",
  };

  saveProject() async {
    final FirebaseFirestore _db = FirebaseFirestore.instance;
    await _db
        .collection(appConfig.COLLECT_NEWARC_PROJECTS)
        .doc(widget.project!.id)
        .update(widget.project!.toMap());

    widget.updateProject!(widget.project);
    setInitialValues();
  }

  collapseAll() {
    keepOpenCommentBoxIndex = -1;
    for (var i = 0; i < isAnimated.length; i++) {
      isAnimated[i] = false;
      isCommentAnimated[i] = false;
      isImageAnimated[i] = false;
    }
  }

  Widget jobWrapper(BuildContext context, ProjectJob projectRow) {
    int count = widget.project!.projectJobs!.indexOf(projectRow);
    progressMessage = '';

    if (tmpTitle == projectRow.status) {
      showTitle = false;
    } else {
      showTitle = true;
      tmpTitle = projectRow.status!;
    }

    String jobStartDate = '';
    if (jobDateStartOn[projectRow.indexPlace!] > 0) {
      DateTime startOn = DateTime.fromMillisecondsSinceEpoch(
          jobDateStartOn[projectRow.indexPlace!]);
      jobStartDate = (startOn.day > 9
              ? startOn.day.toString()
              : '0' + startOn.day.toString()) +
          '/' +
          (startOn.month > 9
              ? startOn.month.toString()
              : '0' + startOn.month.toString()) +
          '/' +
          startOn.year.toString();
    }

    String jobEndDate = '';
    if (jobDateEndOn[projectRow.indexPlace!] > 0) {
      DateTime endOn = DateTime.fromMillisecondsSinceEpoch(
          jobDateEndOn[projectRow.indexPlace!]);
      jobEndDate =
          (endOn.day > 9 ? endOn.day.toString() : '0' + endOn.day.toString()) +
              '/' +
              (endOn.month > 9
                  ? endOn.month.toString()
                  : '0' + endOn.month.toString()) +
              '/' +
              endOn.year.toString();
    }

    Color containerColor = Color.fromRGBO(242, 242, 242, 1);

    if ( projectRow.status.toString().toLowerCase().indexOf('in corso') > -1 ) {
      containerColor = Color(0xffFAEFE2);
    } else if( projectRow.status.toString().toLowerCase().indexOf('da fare') > -1 ) {
      containerColor = Color(0xffF2F2F2);
    } else if (projectRow.status .toString() .toLowerCase() .indexOf('completati') > -1) {
      containerColor = Color(0xffE5F2E7);
    }

    Color dotColor = Color.fromRGBO(242, 242, 242, 1);

    if ( projectRow.status.toString().toLowerCase().indexOf('in corso') > -1 ) {
      dotColor = Color(0xffFF8800);
    } else if( projectRow.status.toString().toLowerCase().indexOf('da fare') > -1 ) {
      dotColor = Color(0xffBEBEBE);
    } else if (projectRow.status .toString() .toLowerCase() .indexOf('completati') > -1) {
      dotColor = Color(0xff39C14F);
    }

    Map title = {};
    try {
      title = statusDelivery.where((st) => st['value'] == projectRow.status!).first;  
    } catch (e,s) {
      return Container();
    }
    

    

    bool showStartDate = false;
    bool showEndDate = false;
    bool showQuality = false;
    bool showCamera = true;

    if (projectRow.status.toString().toLowerCase().indexOf('completati') > -1 ||
        projectRow.status.toString().toLowerCase().indexOf('in corso') > -1) {
      showStartDate = true;
    }

    if (projectRow.status.toString().toLowerCase().indexOf('completati') > -1) {
      showEndDate = true;
      showQuality = true;
      showCamera = true;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        showTitle!
            ? Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 25),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          color: dotColor
                        ),
                        margin: EdgeInsets.only(right: 10),
                        height: 12,
                        width: 12
                      ),


                      NarFormLabelWidget(
                        fontSize: 19,
                        fontWeight: 'bold',
                        label: title['label'].toString().toCapitalized()
                      ),
                    ],
                  ),
                ],
              )
            : SizedBox(height: 15),
        SizedBox(
          height: showTitle! ? 8 : 0,
        ),
        Container(
          decoration: BoxDecoration(
            color: containerColor,
            borderRadius: BorderRadius.circular(10),
          ),
          // height: 80,
          margin: EdgeInsets.only(bottom: 5),
          child: Stack(
            children: [
              Column(
                children: [
                  Padding(
                    padding:
                        const EdgeInsets.only(top: 10, left: 10, right: 10, bottom: 15),
                    child: Column(
                      children: [
                        Stack(
                          clipBehavior: Clip.none,
                          children: [
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Row(
                                  children: [
                                    MouseRegion(
                                      cursor: SystemMouseCursors.click,
                                      child: GestureDetector(
                                        onTap: (){
                                          int currentIndex = widget.project!.projectJobs!.indexOf(projectRow);

                                          String currentStatus = projectRow.status!;
                                          List currentStatusIndexes = [];
                                          for (var i = 0; i < widget.project!.projectJobs!.length; i++) {
                                            if( widget.project!.projectJobs![i].status == currentStatus ) {
                                              currentStatusIndexes.add(i);
                                            }
                                          }

                                          if( currentStatusIndexes.length > 1 ) {
                                            
                                            if( currentIndex == currentStatusIndexes[0] ) {
                                              int lastItem = currentStatusIndexes[currentStatusIndexes.length - 1];
                                              ProjectJob tmpJob = projectRow;
                                              for (var j = 0; j < currentStatusIndexes.length - 1 ; j++) {
                                                
                                                widget.project!.projectJobs![currentStatusIndexes[j]] = widget.project!.projectJobs![currentStatusIndexes[j+1]];
                                              }
                                              widget.project!.projectJobs![lastItem] = projectRow;
                                
                                            } else {
                                
                                              ProjectJob tmpJob = widget.project!.projectJobs![currentIndex-1];
                                              widget.project!.projectJobs![currentIndex] = tmpJob;
                                              widget.project!.projectJobs![currentIndex-1] = projectRow;
                                
                                
                                            }
                                            

                                            setInitialValues();
                                            saveProject();
                                
                                            setState(() {});
                                
                                          }
                                        },
                                        child: Container(
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(6),
                                            color: Colors.white
                                          ),
                                          padding: EdgeInsets.all(7),
                                          child: Transform.rotate(
                                            angle: 3.14,
                                            child: Image.asset(
                                              'assets/icons/download.png',
                                              height: 20,
                                              color: Color(0xff6E6E6E),
                                            ),
                                          ),
                                          height: 27,
                                          width: 27,
                                          
                                        ),
                                      ),
                                    ),
                                    
                                    SizedBox(width:5),
                                    MouseRegion(
                                      cursor: SystemMouseCursors.click,
                                      child: GestureDetector(
                                        onTap: (){
                                          int currentIndex = widget.project!.projectJobs!.indexOf(projectRow);
                                
                                          String currentStatus = projectRow.status!;
                                          List currentStatusIndexes = [];
                                          for (var i = 0; i < widget.project!.projectJobs!.length; i++) {
                                            if( widget.project!.projectJobs![i].status == currentStatus ) {
                                              currentStatusIndexes.add(i);
                                            }
                                          }
                                
                                          if( currentStatusIndexes.length > 1 ) {
                                            
                                            if( currentIndex == currentStatusIndexes[currentStatusIndexes.length-1] ) {
                                              
                                              int lastItem = currentStatusIndexes[currentStatusIndexes.length - 1];
                                              
                                              for (var j = currentStatusIndexes.length - 1; j > 0 ; j--) {
                                                
                                                widget.project!.projectJobs![currentStatusIndexes[j]] = widget.project!.projectJobs![currentStatusIndexes[j-1]];
                                              }
                                              widget.project!.projectJobs![currentStatusIndexes[0]] = projectRow;
                                
                                            } else {
                                
                                              widget.project!.projectJobs![currentIndex] = widget.project!.projectJobs![currentIndex+1];
                                              widget.project!.projectJobs![currentIndex+1] = projectRow;
                                
                                
                                            }


                                            setInitialValues();
                                            saveProject();
                                
                                            setState(() {});
                                
                                          }
                                        },
                                        child: Container(
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(7),
                                            color: Colors.white
                                          ),
                                          padding: EdgeInsets.all(7),
                                          child: Image.asset(
                                            'assets/icons/download.png',
                                            height: 20,
                                            color: Color(0xff6E6E6E),
                                          ),
                                          height: 27,
                                          width: 27,
                                          
                                        ),
                                      ),
                                    ),
                                    
                                  ],
                                ),
                                SizedBox(width: 20),
                                Expanded(
                                  // flex: 2,
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      NarFormLabelWidget(
                                        label: projectRow.activityCategory??'',
                                        fontSize: 11,
                                        textColor: Color(0xff666666),
                                        letterSpacing: 0.03,
                                        fontWeight: '500',
                                      ),
                                      SizedBox(
                                        height: 5,
                                      ),
                                      NarFormLabelWidget(
                                        label: projectRow.activity,
                                        fontSize: 14,
                                        fontWeight: 'bold',
                                        textColor: Colors.black,
                                      )
                                    ],
                                  ),
                                ),
                                // SizedBox(width: 10,),
                                Expanded(
                                  // flex: 1,
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      NarFormLabelWidget(
                                        label: 'Eseguito da',
                                        fontSize: 11,
                                        textColor: Color(0xff666666),
                                        letterSpacing: 0.03,
                                        fontWeight: '500',
                                      ),
                                      SizedBox(
                                        height: 5,
                                      ),
                                      NarFormLabelWidget(
                                        label: projectRow.vendor,
                                        fontSize: 14,
                                        fontWeight: 'bold',
                                        textColor: Colors.black,
                                      )
                                    ],
                                  ),
                                ),
                                
                              ],
                            ),
                            Positioned(
                              right: 0,
                              top: 0,
                              child: Row(
                                children: [
                                  if( !projectRow.showInApp! ) Container(
                                    height: 18, 
                                    width: 18,
                                    margin: EdgeInsets.only(right: 10),
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(18),
                                    ),
                                    child: Center(
                                      child: SvgPicture.asset(
                                        'assets/icons/closed-eye.svg',
                                        width: 12,
                                      ),
                                    ),
                                  ),
                                                          
                                  Container(
                                    height: 18, 
                                    padding: EdgeInsets.symmetric(horizontal: 7),
                                    margin: EdgeInsets.only(right: 10),
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(18),
                                    ),
                                    child: Center(
                                      child: NarFormLabelWidget(
                                        label: projectRow.timeline.toString() +' giorni',
                                        textColor: Color(0xff666666),
                                        fontSize: 11,
                                        letterSpacing: 0.03,
                                      ),
                                    ),
                                  ),
                                                          
                                  if( daysSinceHypotheticalDate(projectRow) > 0 ) Container(
                                    height: 18, 
                                    padding: EdgeInsets.symmetric(horizontal: 7),
                                    decoration: BoxDecoration(
                                      color: Color(0xffE82525),
                                      borderRadius: BorderRadius.circular(18),
                                    ),
                                    child: Center(
                                      child: NarFormLabelWidget(
                                        label: '+'+daysSinceHypotheticalDate(projectRow).toString(),
                                        textColor: Colors.white,
                                        fontSize: 11,
                                        letterSpacing: 0.03,
                                        fontWeight: '600',
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        
                        if( isAnimated[projectRow.indexPlace!] )
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(6),
                            
                              ),
                              margin: EdgeInsets.only(top:10),
                              padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                              child: Row(
                                children: [
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      NarFormLabelWidget(
                                        label: 'Inizio ipotetico',
                                        fontSize: 11,
                                        letterSpacing: 0.03,
                                        textColor: Color(0xff666666)
                                      ),
                                      SizedBox(height:5),
                                      NarFormLabelWidget(
                                        label: projectRow.hypotheticalStartDate! > 0 ? getFormattedDate(projectRow.hypotheticalStartDate) : '-',
                                        fontSize: 12,
                                        letterSpacing: 0.01,
                                        textColor: Colors.black
                                      ),
                                    ],
                                  ),
                                  SizedBox(width: 10),
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      NarFormLabelWidget(
                                        label: 'Inizio reale',
                                        fontSize: 11,
                                        letterSpacing: 0.03,
                                        textColor: Color(0xff666666)
                                      ),
                                      SizedBox(height:5),
                                      NarFormLabelWidget(
                                        label: projectRow.startDate! > 0 ? getFormattedDate(projectRow.startDate) : '-',
                                        fontSize: 12,
                                        letterSpacing: 0.01,
                                        textColor: Colors.black
                                      ),
                                    ],
                                  ),
                                  SizedBox(width: 10),
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      NarFormLabelWidget(
                                        label: 'Fine reale',
                                        fontSize: 11,
                                        letterSpacing: 0.03,
                                        textColor: Color(0xff666666)
                                      ),
                                      SizedBox(height:5),
                                      NarFormLabelWidget(
                                        label: projectRow.endDate! > 0 ? getFormattedDate(projectRow.endDate) : '-',
                                        fontSize: 12,
                                        letterSpacing: 0.01,
                                        textColor: Colors.black
                                      ),
                                    ],
                                  ),
                                ]
                              ),
                            
                            ),
                            Container(
                              margin: EdgeInsets.only(bottom: 10, top: 15),
                              child: Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  mainAxisSize: MainAxisSize.max,
                                  children: [
                                    
                                    Stack(
                                      clipBehavior: Clip.none,
                                      children: [
                                        MouseRegion(
                                          cursor: SystemMouseCursors.click,
                                          child: GestureDetector(
                                            onTap: (){
                                              popupComments(context, setState, projectRow);
                                            },
                                            child: Container(
                                              margin: EdgeInsets.only(right: 8),
                                              decoration: BoxDecoration(
                                                borderRadius: BorderRadius.circular(6),
                                                color: Colors.white
                                              ),
                                              padding: EdgeInsets.all(5),
                                              child: SvgPicture.asset(
                                                'assets/icons/comment-bubble.svg',
                                                color: Color(0xff6D6D6D),
                                                height: 16
                                              ),
                                              height: 27,
                                              width: 27,
                                              
                                            ),
                                          ),
                                        ),
                                        projectRow.jobComments!.length > 0
                                            ? Positioned(
                                                top: -5,
                                                right: 5,
                                                child: Container(
                                                  height: 12,
                                                  width: 12,
                                                  decoration: BoxDecoration(
                                                    color: Theme.of(context).primaryColor,
                                                    borderRadius:
                                                        BorderRadius.circular(12),
                                                  ),
                                                  child: Center(
                                                    child: NarFormLabelWidget(
                                                    label: projectRow .jobComments!.length .toString(),
                                                    textColor: Colors.white,
                                                    fontSize: 10,
                                                    letterSpacing: 0.02,
                                                  )),
                                                ),
                                              )
                                            : SizedBox(
                                                height: 0,
                                              )
                                      ],
                                    ),

                                    if( showCamera ) 
                                    Stack(
                                      clipBehavior: Clip.none,
                                      children: [
                                        MouseRegion(
                                          cursor: SystemMouseCursors.click,
                                          child: GestureDetector(
                                            onTap: (){
                                              popupImages(context, setState, projectRow);


                                            },
                                            child: Container(
                                              decoration: BoxDecoration(
                                                borderRadius: BorderRadius.circular(6),
                                                color: Colors.white
                                              ),
                                              padding: EdgeInsets.all(5),
                                              margin: EdgeInsets.only(right: 15),
                                              child: Image.asset(
                                                'assets/icons/camera.png',
                                                color: Color(0xff6D6D6D),
                                                height: 18
                                              ),
                                              
                                              height: 27,
                                              width: 27,
                                              
                                            ),
                                          ),
                                        ),
                                        if( projectRow.images!.length > 0 )
                                        Positioned(
                                          top: -5,
                                          right: 10,
                                          child: Container(
                                            height: 12,
                                            width: 12,
                                            decoration: BoxDecoration(
                                              color: Theme.of(context).primaryColor,
                                              borderRadius: BorderRadius.circular( 12),
                                            ),
                                            child: Center(
                                                child: NarFormLabelWidget(
                                              label: projectRow.images!.length .toString(),
                                              textColor: Colors.white,
                                              fontSize: 10,
                                            )),
                                          ),
                                        )
                                            
                                      ],
                                    ),

                                    MouseRegion(
                                      cursor: SystemMouseCursors.click,
                                      child: GestureDetector(
                                        onTap: (){
                                          addJobPopup(context, projectRow);
                                        },
                                        child: Container(
                                          margin: EdgeInsets.only(right: 8),
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(6),
                                            color: Colors.white
                                          ),
                                          padding: EdgeInsets.all(5),
                                          child: Image.asset(
                                            'assets/icons/edit.png',
                                            color: Color(0xff6D6D6D),
                                            height: 16
                                          ),
                                          height: 27,
                                          width: 27,
                                          
                                        ),
                                      ),
                                    ),
                                    
                                    // if( projectRow.status!.toLowerCase().indexOf('da fare') == -1 )
                                    MouseRegion(
                                      cursor: SystemMouseCursors.click,
                                      child: GestureDetector(
                                        onTap: (){
                                          tmpTitle = '';
                                          showTitle = false;
                        
                                          deleteDialog(context, projectRow);
                                        },
                                        child: Container(
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(6),
                                            color: Colors.white
                                          ),
                                          padding: EdgeInsets.all(5),
                                          margin: EdgeInsets.only(right: 35),
                                          child: Image.asset(
                                            'assets/icons/trash-process.png',
                                            color: Color(0xffEA3132),
                                            height: 16
                                          ),
                                          height: 27,
                                          width: 27,
                                          
                                        ),
                                      ),
                                    ),
                                    

                                    
                                    
                                    
                                  ],
                                ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  
                ],
              ),
              Positioned(
                right: 0,
                bottom: isAnimated[projectRow.indexPlace!] ? 15 : 0,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    MouseRegion(
                      cursor: SystemMouseCursors.click,
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            
                            if( isAnimated[projectRow.indexPlace!] == true ) {
                              keepOpenCommentBoxIndex = -1;
                              isAnimated[projectRow.indexPlace!] = false;
                            } else {
                              keepOpenCommentBoxIndex = projectRow.indexPlace!;
                              isAnimated[projectRow.indexPlace!] = true;
                            }

                            print({'isAnimated[projectRow.indexPlace!]', isAnimated[projectRow.indexPlace!], keepOpenCommentBoxIndex});
                            
                          });
                        },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              vertical: 8.0, horizontal: 15),
                          child: Image.asset(
                            isAnimated[projectRow.indexPlace!] ? 'assets/icons/arrow_up.png' : 'assets/icons/arrow_down.png',
                            height: 13,
                            color: Color(0xFF5b5b5b),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
      ],
    );
  }

  onUploadCompleted() {
    // print({'saved', allFiles});
    widget.project!.projectJobs![keepOpenImageBoxIndex].images =
        allFiles[keepOpenImageBoxIndex];
    saveProject();
    setState(() {});
  }

  deleteJobFiles(ProjectJob projectRow) async {
    await deleteDirectory(
        'projects/${widget.project!.id}/jobs/' + projectRow.uid!);
  }

  Widget noCommentBox(String message) {
    return Container(
        decoration: BoxDecoration(
          color: Color(0xFFFFFFFF),
          borderRadius: BorderRadius.circular(7),
        ),
        padding: EdgeInsets.symmetric(vertical: 5, horizontal: 8),
        margin: EdgeInsets.only(bottom: 5),
        width: 350,
        height: 45,
        child: Center(
          child: Row(children: [
            NarFormLabelWidget(
              label: message,
              fontSize: 11,
              textColor: Color(0xffA8A8A8),
              textAlign: TextAlign.left,
            )
          ]),
        ));
  }

  Future<Widget> commentBox(BuildContext context, ProjectJob projectRow, JobComments comment, StateSetter _setState) async {
    NewarcUser? user;
    String profilePicture = '';
    bool hasImageError = false;

    if (comment.newarcUser == null) {
      DocumentSnapshot<Map<String, dynamic>> collectionSnapshot;
      collectionSnapshot = await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_USERS)
          .doc(comment.commentorId)
          .get();

      if (collectionSnapshot.data() != null) {
        user = NewarcUser.fromDocument( collectionSnapshot.data()!, collectionSnapshot.id);
        try {
          profilePicture = await printUrl(
              'users/', comment.commentorId!, user.profilePicture!);
        } catch (e) {
          hasImageError = true;
        }

        widget.project!.projectJobs![projectRow.indexPlace!]
            .jobComments![comment.index!].newarcUser = user;
        widget.project!.projectJobs![projectRow.indexPlace!]
            .jobComments![comment.index!].profilePicture = profilePicture;
      }
    } else {
      user = comment.newarcUser;
      profilePicture = comment.profilePicture!;
    }

    String commentDate = '';
    if (comment.date! > 0) {
      DateTime commentedOn = DateTime.fromMillisecondsSinceEpoch(comment.date!);
      commentDate = (commentedOn.day > 9
              ? commentedOn.day.toString()
              : '0' + commentedOn.day.toString()) +
          '/' +
          (commentedOn.month > 9
              ? commentedOn.month.toString()
              : '0' + commentedOn.month.toString()) +
          '/' +
          commentedOn.year.toString();
    }

    return Container(
      decoration: BoxDecoration(
        color: Color(0xFFFFFFFF),
        border: Border(
          bottom: BorderSide( color: Color(0xffE2E2E2), width: 1)
        )
        // borderRadius: BorderRadius.circular(7),
      ),
      constraints: BoxConstraints(
        minHeight: 45,
        maxHeight: 60,
      ),
      padding: EdgeInsets.symmetric(vertical: 5),
      margin: EdgeInsets.only(bottom: 5),
      width: 350,
      child: Stack(
        children: [
          ListView(
            shrinkWrap: true,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        height: 30,
                        width: 30,
                        padding: EdgeInsets.all(1),
                        decoration: BoxDecoration(
                          color: Color(0xFFD9D9D9),
                          border: Border.all( width: 1, color: Color(0xffE2E2E2) ),
                          borderRadius: BorderRadius.circular(30),
                        ),
                        // clipBehavior: Clip.hardEdge,
                        child: hasImageError
                            ? Center(
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(30),
                                child: Image.asset('assets/icons/user-icon.png',
                                    height: 29, width: 29, fit: BoxFit.cover),
                              ),
                            )
                            : Center(
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(30),
                                child: Image.network(profilePicture,
                                    height: 29, width: 29, fit: BoxFit.cover,
                                    errorBuilder: (BuildContext context, exception,
                                        StackTrace? stackTrace) {
                                    return ClipRRect(
                                      borderRadius: BorderRadius.circular(30),
                                      child: Image.asset('assets/icons/user-icon.png',
                                          height: 29, width: 29, fit: BoxFit.cover),
                                    );
                                  }),
                              ),
                            ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(left: 5),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            NarFormLabelWidget(
                              label: user!.firstName! + ' ' + user!.lastName! + ' - ' + commentDate,
                              fontSize: 9,
                              textColor: Color(0xFFA0A0A0),
                              letterSpacing: 0.03,
                            ),
                            SizedBox(
                              height: 3,
                            ),
                            NarFormLabelWidget(
                              label: comment.message,
                              fontSize: 11,
                              textColor: Color(0xFF000000),
                              letterSpacing: 0.02,
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
          Positioned(
            right: 0,
            top: 0,
            child: MouseRegion(
              cursor: SystemMouseCursors.click,
              child: GestureDetector(
                onTap: (){
                  keepOpenCommentBoxIndex = projectRow.indexPlace!;
                  deleteCommentDialog(context, projectRow, comment, _setState);
                  // _setState((){});
                },
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(6),
                    color: Colors.white
                  ),
                  padding: EdgeInsets.all(5),
                  margin: EdgeInsets.only(right: 35),
                  child: Image.asset(
                    'assets/icons/trash-process.png',
                    color: Color(0xffAEAEAE),
                    height: 16
                  ),
                  height: 27,
                  width: 27,
                  
                ),
              ),
            ),
            
            
          ),
        ],
      ),
    );
  }

  formatDateForParsing(String dateString) {
    List splittedDate = dateString.split('/');
    // print(splittedDate);
    return splittedDate[2] + '-' + splittedDate[1] + '-' + splittedDate[0];
  }

  
  deleteCommentDialog( BuildContext context, ProjectJob projectRow, JobComments comment, StateSetter _setState) {
    return showDialog(
        context: context,
        builder: (BuildContext _bc1) {
          return StatefulBuilder(
              builder: (BuildContext _bc2, StateSetter __setState) {
            return Center(
                child: BaseNewarcPopup(
              title: 'Rimuovi commento',
              buttonText: 'Rimuovi',
              onPressed: () async {
                int rowIndex = projectRow.indexPlace!;
                int commentIndex = comment.index!;

                widget.project!.projectJobs![rowIndex].jobComments!
                    .removeAt(commentIndex);

                if (widget.project!.projectJobs![rowIndex].jobComments!.length >
                    0) {
                  for (var i = 0;
                      i <
                          widget.project!.projectJobs![rowIndex].jobComments!
                              .length;
                      i++) {
                    widget.project!.projectJobs![rowIndex].jobComments![i]
                        .index = i;
                  }
                }

                final FirebaseFirestore _db = FirebaseFirestore.instance;

                try {
                  saveProject();

                  __setState(() {
                    progressMessage = 'Saved!';
                  });
                  _setState(() {
                    progressMessage = 'Saved!';
                  });
                  setState(() {
                    
                  });
                } catch (e) {
                  __setState(() {
                    progressMessage = 'Error';
                  });
                }

                setInitialValues();


              },
              column: Container(
                  height: 99,
                  width: 465,
                  child: Center(
                    child: NarFormLabelWidget(
                        overflow: TextOverflow.visible,
                        label: 'Vuoi davvero eliminare questa comment?',
                        textAlign: TextAlign.center,
                        fontSize: 18,
                        fontWeight: '600',
                        height: 1.5,
                        textColor: Color(0xFF696969)),
                  )),
            ));
          });
        });
  }

  deleteDialog(BuildContext context, ProjectJob projectRow) {
    return showDialog(
        context: context,
        builder: (BuildContext _bc1) {
          return StatefulBuilder(
              builder: (BuildContext _bc2, StateSetter setState) {
            return Center(
                child: BaseNewarcPopup(
              title: 'Rimuovi lavorazione',
              buttonText: 'Rimuovi',
              onPressed: () async {
                await deleteJobFiles(projectRow);

                keepOpenCommentBoxIndex = -1;
                keepOpenImageBoxIndex = -1;

                int removingIndex = projectRow.indexPlace!; 
                widget.project!.projectJobs!.removeAt(removingIndex);

                widget.project!.vendorAndProfessionals!.removeWhere((e) => e.uid == projectRow.uid );
                

                if (widget.project!.projectJobs!.length > 0) {
                  for (var i = 0; i < widget.project!.projectJobs!.length; i++) {
                    widget.project!.projectJobs![i].indexPlace = i;
                    
                    int vnpIndex = widget.project!.vendorAndProfessionals!.indexWhere((e) => e.uid == widget.project!.projectJobs![i].uid );
                    widget.project!.vendorAndProfessionals![vnpIndex].indexPlace = i;

                  }
                }

                try {
                  saveProject();

                  // ProjectJob currentJob = widget.project!.projectJobs![projectRow.indexPlace!];
                  Supplier selectedSupplier = widget.suppliers!.firstWhere((sup) => sup.id == projectRow.vendorUserId );
                  /**
                  * Remove Occupied flag to the supplier in the document
                  */
                  try {
                    Map _tActivity = _activity.firstWhere((act) => act['value'] == projectRow.activity );
                    int _tIndex = selectedSupplier.assignedProject!.indexWhere((p) => p['project_id'] == widget.project!.id && p['category'] == _tActivity['category'] );

                    if(_tIndex > -1 ) selectedSupplier.assignedProject!.removeAt(_tIndex);

                    final FirebaseFirestore _db = FirebaseFirestore.instance;

                    await _db
                    .collection(appConfig.COLLECT_SUPPLIERS)
                    .doc(projectRow.vendorUserId)
                    .update(selectedSupplier.toMap());
                  } catch (e,s) {
                    print({e,s});
                  }

                  setState(() {
                    progressMessage = 'Saved!';
                  });
                } catch (e) {
                  setState(() {
                    progressMessage = 'Error';
                  });
                }

                setInitialValues();

                // Navigator.pop(context);
              },
              column: Container(
                  height: 99,
                  width: 465,
                  child: Center(
                    child: NarFormLabelWidget(
                        overflow: TextOverflow.visible,
                        label: 'Vuoi davvero eliminare questa lavorazione?',
                        textAlign: TextAlign.center,
                        fontSize: 18,
                        fontWeight: '600',
                        height: 1.5,
                        textColor: Color(0xFF696969)),
                  )),
            ));
          });
        });
  }

  popupAddCategory(BuildContext context, StateSetter initialSetStateFn){

    TextEditingController contCategoryName = new TextEditingController();
    String progressMessage = '';
    return showDialog(
      context: context,
      builder: (BuildContext _bc1) {
        return StatefulBuilder(
          builder: (BuildContext _bc2, StateSetter acSetState) {
            return Center(
              child: BaseNewarcPopup(
                title: 'Aggiungi Category',
                buttonText: 'Aggiungi',
                onPressed: () async {

                  try {
                    int existing = uniqueCategoryList.indexWhere((e) => e['value'].toString().toLowerCase() == contCategoryName.text.toLowerCase() );

                    if( existing > -1 ) {
                      acSetState((){
                        progressMessage = 'Category already exists!';
                      });
                      return false;
                    }

                    acSetState((){
                      progressMessage = 'Saving';
                    });
                    final FirebaseFirestore _db = FirebaseFirestore.instance;
                    await _db
                        .collection(appConfig.COLLECT_CATEGORY_ACTIVITY)
                        .add({
                          'categoryName' : contCategoryName.text
                        });

                    // add newly added category to the list.
                    uniqueCategoryList.add({ 'value': contCategoryName.text, 'label': contCategoryName.text});
                    acSetState((){
                      progressMessage = 'Saved';
                    });
                    
                    initialSetStateFn((){});
                    setState(() {});

                    return true;
                  } catch (e,s) {
                    debugPrint('popupAddCategory');
                    debugPrint(e.toString());
                    debugPrintStack(stackTrace: s);
                    acSetState((){
                      progressMessage = 'Error occured! Try again later.';
                    });
                    return false;
                    
                  }

                  
                },
                column: Container(
                  height: 150,
                  width: 480,
                  padding: EdgeInsets.only(left: 40, right: 40),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CustomTextFormField(
                        label: "Category name",
                        controller: contCategoryName,
                        validator: (value) {
                          if (value == '') {
                            return 'Required!';
                          }

                          return null;
                        },
                      ),
                      SizedBox(
                        height: 5,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          NarFormLabelWidget(
                            label: progressMessage,
                            textAlign: TextAlign.center,
                            textColor: Color(0xff696969),
                            fontSize: 13,
                            fontWeight: '600',
                          ),
                        ],
                      ),
                    ],
                  ),
                )
              )
            );
          }
        );
      }
    ).then((_) {
      setState(() {
        
      });
    });
  
  }

  popupComments(BuildContext context, StateSetter initialSetStateFn, ProjectJob projectRow){

    TextEditingController _contComment = new TextEditingController();
    String progressMessage = '';
    return showDialog(
      context: context,
      builder: (BuildContext _bc1) {
        return StatefulBuilder(
          builder: (BuildContext _bc2, StateSetter acSetState) {
            return Center(
              child: BaseNewarcPopup(
                title: 'Aggiungi un commento',
                buttonText: 'Salva',
                onPressed: () async {

                  if( _contComment.text == '' ) {
                    acSetState(() {
                      progressMessage = 'Empty text!';
                    });
                    return false;
                  }

                  final FirebaseAuth auth = FirebaseAuth.instance;
                  final User user = auth.currentUser!;
                  final uid = user.uid;

                  // keepOpenCommentBoxIndex = projectRow.indexPlace!;

                  JobComments comment = JobComments({
                    'index': widget.project!
                        .projectJobs![projectRow.indexPlace!]
                        .jobComments!
                        .length,
                    'date': DateTime.now().millisecondsSinceEpoch,
                    'commentorId': uid,
                    'message': _contComment.text
                  });

                  widget.project!
                  .projectJobs![projectRow.indexPlace!]
                  .jobComments!
                  .add(comment);

                  final FirebaseFirestore _db = FirebaseFirestore.instance;

                  try {
                    saveProject();

                    _contComment.text = '';
                    // widget.updateProject!(widget.project);
                    acSetState(() {
                      progressMessage = 'Saved!';
                    });
                    // setInitialValues();
                    initialSetStateFn((){});

                    Future.delayed(Duration(seconds: 5), () {
                      acSetState(() {
                        progressMessage = '';
                      });
                    });
                    return false;
                  } catch (e) {
                    acSetState(() {
                      progressMessage = 'Error';
                    });

                    Future.delayed(Duration(seconds: 5), () {
                      acSetState(() {
                        progressMessage = '';
                      });
                    });

                    return false;
                  }
                },
                column: Container(
                  height: 400,
                  width: 550,
                  padding: EdgeInsets.only(left: 40, right: 40),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Container(
                        color: Colors.transparent,
                        child: Row(
                          children: [
                            CustomTextFormField(
                              label: "",
                              controller: _contComment,
                              minLines: 4,
                              controllerFontSize: 13,
                              validator: (value) {
                                return null;
                              },
                            ),
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 5,
                      ),
                      NarFormLabelWidget(
                        label: progressMessage,
                        textAlign: TextAlign.center,
                        textColor: Color(0xff696969),
                        fontSize: 13,
                        fontWeight: '600',
                      ),
                      SizedBox(
                        height: 10,
                      ),
                      Container(
                        height: 200,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(
                            width: 1,
                            color: Color(0xffD7D7D7)
                          )
                        ),
                        padding: EdgeInsets.all(2),
                      child: ListView(
                        shrinkWrap: true,

                        children: projectRow.jobComments!.length > 0
                        ? projectRow.jobComments!.map((e) {
                            return FutureBuilder(
                                future: commentBox( context, projectRow, e, acSetState),
                                initialData: noCommentBox( 'Loading...'),
                                builder: (BuildContext _context, AsyncSnapshot snapshot) {
                                  
                                  if (snapshot.hasData) {
                                    return snapshot.data!;
                                  } else {
                                    return NarFormLabelWidget(
                                        label: "Error while loading comment!"
                                      );
                                  }
                                });
                          }).toList()
                        : [
                            noCommentBox( 'Nessun commento.')
                          ]
                      ),)
                      
                      
                    ],
                  ),
                )
              )
            );
          }
        );
      }
    ).then((_) {
      setState(() {
        
      });
    });
  
  }

  popupImages(BuildContext context, StateSetter initialSetStateFn, ProjectJob projectRow){

    TextEditingController _contComment = new TextEditingController();
    String progressMessage = '';
    return showDialog(
      context: context,
      builder: (BuildContext _bc1) {
        return StatefulBuilder(
          builder: (BuildContext _bc2, StateSetter acSetState) {
            return Center(
              child: BaseNewarcPopup(
                title: 'Aggiungi immagini',
                // buttonText: 'Salva',
                noButton: true,
                onPressed: () async {

                  
                },
                column: Container(
                  height: 400,
                  width: 550,
                  padding: EdgeInsets.only(left: 20, right: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Container(
                        padding: EdgeInsets.symmetric(vertical: 5, horizontal: 5),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            Expanded(
                              child: Container(
                                height: 380,
                                child: ListView(
                                  
                                  shrinkWrap: true,

                                  children: [
                                    Wrap(
                                      children: [
                                        NarFilePickerWidget(
                                          allowMultiple: false,
                                          filesToDisplayInList: 0,
                                          removeButton: true,
                                          isDownloadable: false,
                                          showTitle: false,
                                          removeButtonText: 'Elimina',
                                          uploadButtonPosition: 'back',
                                          showMoreButtonText: '+ espandi',
                                          actionButtonPosition: 'bottom',
                                          displayFormat: 'inline-widget',
                                          containerWidth: 75,
                                          containerHeight: 75,
                                          containerBorderRadius: 13,
                                          borderRadius: 7,
                                          fontSize: 11,
                                          fontWeight: '600',
                                          text: 'Carica foto',
                                          borderSideColor: Theme.of(context) .primaryColor,
                                          hoverColor: Color.fromRGBO( 133, 133, 133, 1),
                                          removeButtonTextColor: Color(0xff797979),
                                          allFiles: allFiles[ projectRow.indexPlace!],
                                          pageContext: context,
                                          storageDirectory: 'projects/${widget.project!.id}/jobs/' + projectRow.uid!,
                                          removeExistingOnChange: true,
                                          progressMessage: fileProgressMessage,
                                          onUploadCompleted: (){
                                            saveProject();
                                            
                                            acSetState((){});
                                            initialSetStateFn((){});
                                          },
                                          compressionQuality: 40,
                                        )
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            SizedBox(
                              width: 10,
                            ),
                            NarFilePickerWidget(
                              allowMultiple: true,
                              displayFormat: 'inline-button',
                              borderRadius: 7,
                              fontSize: 11,
                              fontWeight: '600',
                              text: 'Carica',
                              splashColor: Color(0xffE5E5E5),
                              borderSideColor: Color(0xffE5E5E5),
                              hoverColor: Color(0xffE5E5E5),
                              textColor: Colors.black,
                              allFiles: allFiles[ projectRow.indexPlace!],
                              pageContext: context,
                              storageDirectory: 'projects/${widget.project!.id}/jobs/' + projectRow.uid!,
                              progressMessage: fileProgressMessage,
                              // displayInlineWidget: displayInlineWidget,
                              onUploadCompleted: (){
                                saveProject();
                                
                                acSetState((){});
                                initialSetStateFn((){});
                              },
                              compressionQuality: 40,
                            )
                          ],
                        ))
                      
                      
                    ],
                  ),
                )
              )
            );
          }
        );
      }
    ).then((_) {
      setState(() {
        
      });
    });
  
  }
  // (int hypotheticalMillis, String projectStatus, int endDate) {
  int daysSinceHypotheticalDate(ProjectJob projectRow) {

    int hypotheticalMillis = projectRow.hypotheticalStartDate??0;
    int timelineDays = int.tryParse(projectRow.timeline??'0')??0;
    int endDate = projectRow.endDate??0;
    int startDate = projectRow.startDate??0;

    if( hypotheticalMillis == 0 ) return 0;
    
    DateTime hypotheticalDate = DateTime.fromMillisecondsSinceEpoch(hypotheticalMillis);

    DateTime today = DateTime.now();
    if( projectRow.status!.toLowerCase().contains('completati') ) {
      today = DateTime.fromMillisecondsSinceEpoch(endDate);
    }  

    DateTime todayDate = DateTime(today.year, today.month, today.day);
    

    int businessDays = 1;
    
    // Iterate through each day from hypothetical date to today
    for (DateTime date = hypotheticalDate; date.isBefore(todayDate); date = date.add(Duration(days: 1))) {
      // Check if the day is not Saturday (6) or Sunday (7)
      if (date.weekday != DateTime.saturday && date.weekday != DateTime.sunday) {
        businessDays++;
      }
    }

    businessDays = businessDays - timelineDays;

    if( businessDays > 0 ) return businessDays;

    return 0;

    
  }
  
  popupAddActivity(String activityName, BuildContext context, StateSetter initialSetStateFn){

    TextEditingController contCategoryName = new TextEditingController(text: activityName);
    TextEditingController contActivityName = new TextEditingController();
    TextEditingController contTimeline = new TextEditingController();
    
    String progressMessage = '';
    return showDialog(
      context: context,
      builder: (BuildContext _bc1) {
        return StatefulBuilder(
          builder: (BuildContext _bc2, StateSetter acSetState) {
            return Center(
              child: BaseNewarcPopup(
                title: 'Nuova lavorazione',
                buttonText: 'Aggiungi',
                onPressed: () async {

                  try {
                    
                    acSetState((){
                      progressMessage = 'Saving';
                    });
                    final FirebaseFirestore _db = FirebaseFirestore.instance;
                    QuerySnapshot<Map<String, dynamic>> snapshot = await _db
                        .collection(appConfig.COLLECT_CATEGORY_ACTIVITY)
                        .where('categoryName', isEqualTo: contCategoryName.text)
                        .get();

                    if( snapshot.docs.length > 0 ) {
                      
                      DocumentSnapshot<Map<String, dynamic>> data = snapshot.docs[0];

                      List<Map<String, dynamic>> existingActivities = 
                      (data.data()?['activities'] as List<dynamic>?)
                          ?.map((e) => Map<String, dynamic>.from(e))
                          .toList() ?? [];


                      int existing = existingActivities.indexWhere((e) => e['activity'].toLowerCase() == contActivityName.text.toLowerCase() );

                      if( existing > -1 ) {
                        acSetState((){
                          progressMessage = 'Activity already exists!';
                        });
                        return false;
                      }

                      existingActivities.add( { 'activity': contActivityName.text, 'timeline': contTimeline.text } );
                      await _db
                        .collection(appConfig.COLLECT_CATEGORY_ACTIVITY)
                        .doc( data.id )
                        .update({
                          'activities': existingActivities
                        });

                      _activity.add({
                        'value': contActivityName.text,
                        'label': contActivityName.text,
                        'category': contCategoryName.text,
                        'timeline': double.tryParse(contTimeline.text)
                      });

                      activity.clear();
                      activity.add({'label': '', 'value': ''});

                      _activity.map((e) {
                        if (e['category'] == contCategory.text) {
                          if( e['label'] != '' ) {
                            activity.add(e);
                          }
                        }
                      }).toList();

                      contActivity.text = '';
                      selectedJobs.clear();

                      initialSetStateFn((){});
                      setState(() {});
                      acSetState((){
                        progressMessage = 'Saved';
                      });  

                    } else {
                      acSetState((){
                        progressMessage = 'Error finding category! Try later!';
                      });    
                      // add newly added category to the list.
                      initialSetStateFn((){});
                      return false;
                    }

                    
                    // add newly added category to the list.
                    
                    return true;
                  } catch (e,s) {
                    debugPrint('popupAddCategory');
                    debugPrint(e.toString());
                    debugPrintStack(stackTrace: s);
                    acSetState((){
                      progressMessage = 'Error occured! Try again later.';
                    });
                    return false;
                    
                  }

                  
                },
                column: Container(
                  height: 290,
                  width: 480,
                  padding: EdgeInsets.only(left: 40, right: 40),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [

                      Row(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            flex: 1,
                            child: Column(
                              // mainAxisSize: MainAxisSize.max,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                NarFormLabelWidget(
                                  label: "Seleziona categoria",
                                  textColor: Color(0xff696969),
                                  fontSize: 14,
                                  fontWeight: '600',
                                ),
                                SizedBox(height: 4),
                                NarImageSelectBoxWidget(
                                  options: uniqueCategoryList,
                                  controller: contCategoryName,
                                  validationType: 'required',
                                  parametersValidate: 'Required!',
                                  onChanged: (val) {
                                    
                                  },
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 10,),
                      Row(
                        children: [
                          CustomTextFormField(
                            label: "Activity name",
                            controller: contActivityName,
                            validator: (value) {
                              if (value == '') {
                                return 'Required!';
                              }
                          
                              return null;
                            },
                          ),
                        ],
                      ),
                      SizedBox(height: 10,),
                      Row(
                        children: [
                          CustomTextFormField(
                            label: "Timeline",
                            controller: contTimeline,
                            validator: (value) {
                              if (value == '') {
                                return 'Required!';
                              }
                          
                              return null;
                            },
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 5,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          NarFormLabelWidget(
                            label: progressMessage,
                            textAlign: TextAlign.center,
                            textColor: Color(0xff696969),
                            fontSize: 13,
                            fontWeight: '600',
                          ),
                        ],
                      ),
                    ],
                  ),
                )
              )
            );
          }
        );
      }
    ).then((_) {
      setState(() {
        
      });
    });
  
  }

  addJobPopup(BuildContext context, var jobRow) async {
    contVendor.text = '';
    contCategory.text = '';
    contActivity.text = '';
    contTimelineMain.text = '';
    String vendorMessage = '';
    bool showInApp = false;

    TextEditingController contStatusP = new TextEditingController();
    TextEditingController contHypotheticalJobStartDateP = new TextEditingController(text: '');
    TextEditingController contStartDateP = new TextEditingController(text: '');
    TextEditingController contEndDateP = new TextEditingController(text: '');

    int hypotheticalJobStartDateP = DateTime.now().millisecondsSinceEpoch;
    int startDateP = 0;
    int endDateP = 0;

    if( jobRow != null ) {
      ProjectJob pj = jobRow;
      
      hypotheticalJobStartDateP = pj.hypotheticalStartDate??0;
      startDateP = pj.startDate??0;
      endDateP = pj.endDate??0;

      contStatusP.text = pj.status!;
      contHypotheticalJobStartDateP.text = getFormattedDate(hypotheticalJobStartDateP);
      
      if( startDateP > 0 ) {
        contStartDateP.text = getFormattedDate(startDateP);
      }
      //  else {
      //   contStartDateP.text = getFormattedDate( DateTime.now().millisecondsSinceEpoch );
      // }

      if( endDateP > 0 ) {
        contEndDateP.text = getFormattedDate(endDateP);
      } 
      // else {
      //   contEndDateP.text = getFormattedDate( DateTime.now().millisecondsSinceEpoch );
      // }
      
      contTimelineMain.text = pj.timeline??'1';
      showInApp = !(pj.showInApp ?? false);

    } else {
      contHypotheticalJobStartDateP.text = getFormattedDate( DateTime.now().millisecondsSinceEpoch );
      
      contEndDateP.text = '';
    }

    return showDialog(
        context: context,
        builder: (BuildContext _bc1) {
          return StatefulBuilder(
              builder: (BuildContext _bc2, StateSetter _setState) {
            return Center(
              child: BaseNewarcPopup(
              title: jobRow != null ? 'Modifica lavorazione' : 'Aggiungi lavorazione',
              buttonText: jobRow != null ? 'Modifica' : 'Aggiungi',
              // noButton: !hasVendor!,
              onPressed: () async {
                _setState(() {
                  progressMessage = 'Salvataggio in corso...';
                });

                // print({'selectedJobs',selectedJobs});
                // return true; 

                // if( selectedJobs.length == 0 ) {
                //   progressMessage = 'Nulla da salvare.';
                //   return false;
                // } 
                

                try {
                  bool isEditMode = false;
                  if ( jobRow != null ) {
                    isEditMode = true;
                  }

                  print({'isEditMode', isEditMode});

                  // selectedSuppliers

                  final FirebaseFirestore _db = FirebaseFirestore.instance;
                    DocumentSnapshot<Map<String, dynamic>> snapshot = await _db
                      .collection(appConfig.COLLECT_NEWARC_PROJECTS)
                      .doc(widget.project!.id)
                      .get();
                    
                    if (snapshot.data() != null) {
                      NewarcProject _tmpProject = NewarcProject.fromDocument(snapshot.data()!, snapshot.id);
                      widget.project!.userNotifications = _tmpProject.userNotifications;
                    }
                    

                    /* Add new mode */
                    if (!isEditMode) {

                      Supplier selectedSupplier = widget.suppliers!.firstWhere((sup) => sup.id == selectedVendor!.id );

                      ProjectJob projectJob = ProjectJob({
                        'uid': generateRandomString(8),
                        'activityCategory': contCategory.text,
                        'activity': contActivity.text,
                        'vendorUserId': selectedVendor!.id,
                        'vendor': selectedSupplier.name,
                        'status': '3 Da fare',
                        'quality': null,
                        'startDate': startDateP,
                        'endDate': endDateP,
                        'images': [],
                        'timeline': contTimelineMain.text,
                        'hypotheticalStartDate': hypotheticalJobStartDateP,
                        'showInApp': !showInApp,
                        'jobComments': null,
                      }); 
                      projectJob.indexPlace = widget.project!.projectJobs!.length;
                      widget.project!.projectJobs!.add(projectJob);
                      // handle notification saving in project
                      NotificationRenovationApp notification = NotificationRenovationApp.empty();
                      notification.jobName = projectJob.activity;
                      notification.type = statusDelivery[statusDelivery.indexWhere((e) => e['value'] == '3 Da fare')]['label'];
                      widget.project!.userNotifications!.add(notification);
                      sendProjectJobModificationNotification(
                        widget.project!.id!, 
                        "Nuova lavorazione!", 
                        "La lavorazione ${projectJob.activity} è stata aggiunta!",
                      );

                      final alreadyExists = widget.project!.vendorAndProfessionals!
                          .any((element) => element.vendorUserId == selectedVendor!.id);

                      if(!alreadyExists){
                        Process vendorAndProfessionals = Process({
                          'uid': projectJob.uid,
                          'indexPlace': projectJob.indexPlace,
                          'vendorUserId': selectedVendor!.id,
                          'vendor': selectedSupplier.name,
                          'activity': contCategory.text,
                          'cost': 0.0,
                          'agreedInstallments': 1,
                          'installments': [PaymentInstallment.empty()],
                          'contractor': false,
                          'hasPenalty': false,
                          'agreedPenalty': 0,
                          'jobStartTimestamp': 0,
                          'jobEndTimestamp': 0,
                          'isUnique': false
                        });

                        widget.project!.vendorAndProfessionals!.add(vendorAndProfessionals);
                      }


                      Map _t = {
                        'project_id': widget.project!.id,
                        'category': contCategory.text
                      };

                      
                      /**
                      * Add Occupied flag to the supplier in the document
                      */
                      selectedSupplier.assignedProject!.add(_t);
                      // final FirebaseFirestore _db = FirebaseFirestore.instance;
                      
                      await _db
                      .collection(appConfig.COLLECT_SUPPLIERS)
                      .doc(selectedVendor!.id)
                      .update(selectedSupplier.toMap());
                    } else {
                      ProjectJob pj = jobRow;
                      int currentEditing = widget.project!.projectJobs!.indexWhere((e) => e.uid == jobRow.uid );
                      ProjectJob _projectJob = ProjectJob({
                        'uid': pj.uid,
                        'activityCategory': pj.activityCategory,
                        'activity': pj.activity,
                        'vendorUserId': pj.vendorUserId,
                        'vendor': pj.vendor,
                        'status': contStatusP.text,
                        'quality': null,
                        'startDate': startDateP,
                        'endDate': endDateP,
                        'images': pj.images,
                        'timeline': contTimelineMain.text,
                        'hypotheticalStartDate': hypotheticalJobStartDateP,
                        'showInApp': !showInApp,
                        'jobComments': pj.jobComments,
                      });

                      widget.project!.projectJobs![currentEditing] = _projectJob;
                      String previousJobStatus = pj.status ?? "";
                      String currentJobStatus = _projectJob.status ?? "";
                      int selectedStatusIndex = statusDelivery.indexWhere((e) => e['value'] == currentJobStatus);
                      NotificationRenovationApp notification = NotificationRenovationApp.empty();
                      if (((previousJobStatus.toLowerCase().contains("da fare")) && ((currentJobStatus.toLowerCase().contains("in corso")) || (currentJobStatus.toLowerCase().contains("completati")))) 
                            || 
                          ((previousJobStatus.toLowerCase().contains("in corso")) && currentJobStatus.toLowerCase().contains("completati"))) 
                        {
                          String notificationTitle = "Lavorazione iniziata!";
                          String message = "La lavorazione ${_projectJob.activity} è iniziata!";
                          notification.type = statusDelivery[selectedStatusIndex]['label'];
                          if (currentJobStatus.toLowerCase().contains('completati') ) {
                            notificationTitle = "Lavorazione completata!";
                            message = "La lavorazione ${_projectJob.activity} è completata!";
                          }
                        notification.jobName = _projectJob.activity;
                        widget.project!.userNotifications!.add(notification);
                        sendProjectJobModificationNotification(
                          widget.project!.id!,
                          notificationTitle,
                          message,
                        );
                    }}

                  saveProject();
                  
                  _setState(() {
                    progressMessage = 'Saved!';
                  });
                  setState(() {});

                } catch (e, s) {
                  print({e, s});
                }

                setInitialValues();

                return true;
              },
              column: Container(
                height: MediaQuery.of(context).size.height*.7,
                width: 480,
                padding: EdgeInsets.only(left: 40, right: 40),
                child: ListView(
                  children: [
                    
                    if( jobRow != null ) Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          margin: EdgeInsets.only(bottom: 20),
                          child: NarFormLabelWidget(
                            label: jobRow.activity,
                            textColor: Colors.black,
                            fontSize: 16,
                            fontWeight: '600',
                          ),
                        ),
                      ],
                    ),

                    if( jobRow == null ) Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          flex: 1,
                          child: Column(
                            // mainAxisSize: MainAxisSize.max,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  NarFormLabelWidget(
                                    label: "Seleziona categoria",
                                    textColor: Color(0xff696969),
                                    fontSize: 14,
                                    fontWeight: '600',
                                  ),
                                  NarLinkWidget(
                                    text: 'Nuova categoria',
                                    fontSize: 12,
                                    textColor: Theme.of(context).primaryColor,
                                    onClick: (){
                                      popupAddCategory(context, _setState);
                                    },
                                  )
                                ],
                              ),
                              SizedBox(height: 4),
                              NarImageSelectBoxWidget(
                                options: uniqueCategoryList,
                                controller: contCategory,
                                validationType: 'required',
                                parametersValidate: 'Required!',
                                onChanged: (val) {
                                  Map selected = uniqueCategoryList.where((e) => e['value'] == contCategory.text).first;

                                  activity.clear();
                                  _activity.map((e) {
                                    if (e['category'] == contCategory.text) {
                                      if( e['label'] != '' ) {
                                        activity.add(e);
                                      }
                                      
                                    }
                                  }).toList();

                                  if( activity.length > 0 ) {
                                    activity.insert(0, {'label': '', 'value': ''});
                                  }

                                  contActivity.text = '';
                                  selectedJobs.clear();

                                  _setState(() {});
                                },
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    if( jobRow == null ) SizedBox(height: 15),
                    
                    if( jobRow == null ) Row(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            flex: 1,
                            child: Column(
                              // mainAxisSize: MainAxisSize.max,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    NarFormLabelWidget(
                                      label: "Seleziona lavorazioni",
                                      textColor: Color(0xff696969),
                                      fontSize: 14,
                                      fontWeight: '600',
                                    ),
                                    NarLinkWidget(
                                        text: 'Nuova lavorazione',
                                        fontSize: 12,
                                        textColor: Theme.of(context).primaryColor,
                                        onClick: (){
                                          popupAddActivity(contCategory.text, context, _setState);
                                        },
                                      )
                                  ],
                                ),
                                SizedBox(height: 4),
                                Opacity(
                                  opacity: activity.length > 0 ? 1 : 0.5,
                                  child: IgnorePointer(
                                    ignoring: activity.length > 0 ? false : true,
                                    child: NarImageSelectBoxWidget(
                                      
                                      options: activity,
                                      validationType: 'required',
                                      parametersValidate: 'Obbligatorio',
                                      controller: contActivity,
                                      onChanged: ( selectedValues ) {
                                        // Update the controller text with the selected values
                                        // multiselect
                                    
                                        // contActivity.text = selectedValues
                                        //     .map((e) => e['value'])
                                        //     .join(',');
                                    
                                        vendorMessage = '';
                                        hasSingleVendor = false;
                                        hasVendor = false;

                                        int actvityIndex = _activity.indexWhere((e) => e['value'].toString().toLowerCase() == contActivity.text.toString().toLowerCase() );

                                        contTimelineMain.text = _activity[actvityIndex]['timeline'].toString();

                                        _setState(() {});
                                        
                                        vendors.clear();
                                        vendors.add({'label': '', 'value': ''});

                                        widget.suppliers!.map((sup){
                                          if( sup.activities!.indexOf(contCategory.text ) > -1 ) {
                                            Map _temp = {
                                              'value': sup.id,
                                              'label': sup.name! + ' ' + sup.formationType!
                                            };

                                            vendors.add(_temp);


                                          }
                                          
                                        }).toList();

                                        if( vendors.length == 0 ) {
                                          hasVendor = false;
                                          vendorMessage = "Non è ancora stata selezionata una ditta per questa categoria nella pagina Ditte.";
                                        } else {
                                          hasVendor = true;
                                        }
                                        
                                        
                                        
                                    
                                        _setState(() {});
                                      },
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    
                    if( jobRow == null ) SizedBox(height: 15),

                    // if( hasVendor == true && contCategory.text != '' && contActivity.text != '' ) 
                    if( jobRow == null ) Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          flex: 1,
                          child: Column(
                            // mainAxisSize: MainAxisSize.max,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  NarFormLabelWidget(
                                    label: "Eseguita da",
                                    textColor: Color(0xff696969),
                                    fontSize: 14,
                                    fontWeight: '600',
                                  ),
                                  NarLinkWidget(
                                    text: 'Guarda disponibilità',
                                    fontSize: 12,
                                    textColor: Theme.of(context).primaryColor,
                                    onClick: vendors.isNotEmpty 
                                      ? () async {
                                        await _fetchSupplierGanttData();
                                        _showSuppliersGanttDialog(context);
                                      }
                                      : (){},
                                    )
                                ],
                              ),
                              SizedBox(height: 4),
                              NarImageSelectBoxWidget(
                                options: vendors,
                                controller: contVendor,
                                validationType: 'required',
                                parametersValidate: 'Required!',
                                onChanged: (val) {
                                  try {
                                    selectedVendor = widget.suppliers!.where((e) => e.id == contVendor.text).first;
                                  } catch (e, s) {
                                    // print({e, s});
                                  }
                                },
                              ),
                            ],
                          ),
                        )
                       
                      ],
                    ),

                    if( jobRow == null ) SizedBox(height: 15),

                    // if( hasVendor == true && contCategory.text != '' && contActivity.text != '' ) 
                    Column(
                          // mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        NarFormLabelWidget(
                          label: "Durata ipotetica",
                          textColor: Color(0xff696969),
                          fontSize: 14,
                          fontWeight: '600',
                        ),
                        SizedBox(height: 4),
                        NarSelectBoxWidget(
                          options: timelineOptions,
                          controller: contTimelineMain,
                          validationType: 'required',
                          parametersValidate: 'Required!',
                          onChanged: (val) {
                            
                          },
                        ),
                      ],
                    ),

                    if( !contStatusP.text.contains('Completati') ) SizedBox(height: 15),

                    // if( hasVendor == true && contCategory.text != '' && contActivity.text != '' ) 

                    if( !contStatusP.text.contains('Completati') ) Row(
                      children: [
                        CustomTextFormField(
                          label: "Inizio lavori ipotetico",
                          labelFontSize: 14,
                          controller: contHypotheticalJobStartDateP,
                          suffixIcon: Container(
                            padding: const EdgeInsets.all(10),
                            height: 20,
                            width: 20,
                            child: SvgPicture .asset(
                              "assets/icons/calendar.svg",
                              color: Color(0xff7B7B7B),
                              height: 17,
                              width: 17,
                            ),
                          ),
                          // validationMessage: 'Required!',
                          validator: (value) {
                            if (value == '') {
                              return 'Required!';
                            }
                        
                            return null;
                          },
                          onTap: () async {
                            try {
                              DateTime? pickedDate = await showDatePicker(
                                context: context,
                        
                                  initialDate: contHypotheticalJobStartDateP.text == '0' || contHypotheticalJobStartDateP.text == ''
                                      ? DateTime.now()
                                      : DateTime.tryParse(formatDateForParsing(contHypotheticalJobStartDateP.text))!,
                                  firstDate: DateTime(1950),
                                  lastDate: DateTime(2300));
                        
                              if (pickedDate != null) {
                                hypotheticalJobStartDateP = pickedDate.millisecondsSinceEpoch;
                                String formattedDate = DateFormat('dd/MM/yyyy').format(pickedDate);
                        
                                // saveProject();
                        
                                setState(() {
                                  contHypotheticalJobStartDateP.text = formattedDate; //set output date to TextField value.
                                });
                              } else {
                                // widget.project!.hypotheticalJobEndDate = 0;
                                // saveProject();
                              }
                            } catch (e,s) {
                              print({e,s});
                            }
                            
                          },
                          onChangedCallback: (value) {
                            if (value == '') {
                              // widget.project!.hypotheticalJobEndDate = 0;
                              // saveProject();
                            }
                          },
                        ),
                      ],
                    ),

                    if( jobRow != null ) SizedBox(height: 15),
                    
                    // status
                    if( jobRow != null ) Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        NarFormLabelWidget(
                          label: "Modifica stato",
                          textColor: Color(0xff696969),
                          fontSize: 14,
                          fontWeight: '600',
                        ),
                        SizedBox(height: 4),

                        Row(
                          children: [
                            Expanded(
                              child: NarColorBgDropdown(
                                height: 46,
                                labelTextStyle: TextStyle(
                                  fontSize: 14,
                                  color: Colors.black,
                                  fontFamily: 'Raleway-600'
                                ),
                                iconColor: Colors.white,
                                controller: contStatusP,
                                options: statusDelivery,
                                onChanged: () async {

                                  if( startDateP == 0 && contStatusP.text.toLowerCase().contains('in corso') ) {
                                    contStartDateP.text = getFormattedDate( DateTime.now().millisecondsSinceEpoch );
                                    startDateP = DateTime.now().millisecondsSinceEpoch;
                                  } else if( endDateP == 0 && contStatusP.text.toLowerCase().contains('completati') ) {
                                    contEndDateP.text = getFormattedDate( DateTime.now().millisecondsSinceEpoch );
                                    endDateP = DateTime.now().millisecondsSinceEpoch;
                                  }

                                  
                                  _setState((){});
                                },
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),

                    /*
                    In corso
                    Completati
                    */
                    if( jobRow != null && ( contStatusP.text.contains('In corso') || contStatusP.text.contains('Completati') ) ) SizedBox(height: 15),

                    if( jobRow != null && ( contStatusP.text.contains('In corso') || contStatusP.text.contains('Completati') ) ) Row(
                      children: [
                        CustomTextFormField(
                          label: "Inizio lavori reale",
                          labelFontSize: 14,
                          controller: contStartDateP,
                          suffixIcon: Container(
                            padding: const EdgeInsets.all(10),
                            height: 20,
                            width: 20,
                            child: SvgPicture .asset(
                              "assets/icons/calendar.svg",
                              color: Color(0xff7B7B7B),
                              height: 17,
                              width: 17,
                            ),
                          ),
                          // validationMessage: 'Required!',
                          validator: (value) {
                            if (value == '') {
                              return 'Required!';
                            }
                        
                            return null;
                          },
                          onTap: () async {
                        
                            print({ 'contStartDateP.text',contStartDateP.text,contStartDateP.text == '0' || contStartDateP.text == ''});
                        
                            DateTime? pickedDate = await showDatePicker(
                                context: context,
                                initialDate: contStartDateP.text == '0' || contStartDateP.text == ''
                                    ? DateTime.now()
                                    : DateTime.tryParse(formatDateForParsing(contStartDateP.text))!,
                                firstDate: DateTime(1950),
                                lastDate: DateTime(2300));
                        
                            if (pickedDate != null) {
                              startDateP = pickedDate.millisecondsSinceEpoch;
                              String formattedDate = DateFormat('dd/MM/yyyy').format(pickedDate);
                        
                              // saveProject();
                        
                              _setState(() {
                                contStartDateP.text = formattedDate; //set output date to TextField value.
                              });
                              
                            } else {
                              // widget.project!.hypotheticalJobEndDate = 0;
                              // saveProject();
                            }
                          },
                          onChangedCallback: (value) {
                            if (value == '') {
                              // widget.project!.hypotheticalJobEndDate = 0;
                              // saveProject();
                            }
                          },
                        ),
                      ],
                    ),

                    if( jobRow != null && contStatusP.text.contains('Completati') ) SizedBox(height: 15),
                    
                    if( jobRow != null && contStatusP.text.contains('Completati') ) Row(
                      children: [
                        CustomTextFormField(
                          label: "Fine lavori reale",
                          labelFontSize: 14,
                          controller: contEndDateP,
                          suffixIcon: Container(
                            padding: const EdgeInsets.all(10),
                            height: 20,
                            width: 20,
                            child: SvgPicture .asset(
                              "assets/icons/calendar.svg",
                              color: Color(0xff7B7B7B),
                              height: 17,
                              width: 17,
                            ),
                          ),
                          // validationMessage: 'Required!',
                          validator: (value) {
                            if (value == '') {
                              return 'Required!';
                            }
                        
                            return null;
                          },
                          onTap: () async {
                            print({ 'contStartDateP.text',contEndDateP.text,contEndDateP.text == '0' || contEndDateP.text == ''});
                            DateTime? pickedDate = await showDatePicker(
                                context: context,
                                initialDate: contEndDateP.text == '0' || contEndDateP.text == ''
                                    ? DateTime.now()
                                    : DateTime.tryParse(formatDateForParsing(contEndDateP.text))!,
                                firstDate: DateTime(1950),
                                lastDate: DateTime(2300));
                        
                            if (pickedDate != null) {
                              endDateP = pickedDate.millisecondsSinceEpoch;
                              String formattedDate = DateFormat('dd/MM/yyyy').format(pickedDate);
                        
                              // saveProject();
                        
                              _setState(() {
                                contEndDateP.text = formattedDate; //set output date to TextField value.
                              });
                              
                            } else {
                              // widget.project!.hypotheticalJobEndDate = 0;
                              // saveProject();
                            }
                          },
                          onChangedCallback: (value) {
                            if (value == '') {
                              // widget.project!.hypotheticalJobEndDate = 0;
                              saveProject();
                            }
                          },
                        ),
                      ],
                    ),

                    SizedBox(height: 15),
                    
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Transform.scale(
                          // padding: const EdgeInsets.all(0),
                          scale: 0.9,
                          child: Switch(
                            // This bool value toggles the switch.
                            value: showInApp,
                            activeColor: Theme.of(context).primaryColor,
                            onChanged: (bool value) async {
                              
                              _setState(() {
                                showInApp = value;
                              });
                            },
                          ),
                        ),
                        // SizedBox(width: 15),
                        NarFormLabelWidget(
                          label: 'Nascondi in app',
                          fontSize: 14,
                          textColor: Colors.black,
                        )
                      ],
                    ),
                    SizedBox(height: 10),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 30.0),
                      child: NarFormLabelWidget(
                        label: vendorMessage,
                        textAlign: TextAlign.center,
                        textColor: Color(0xff696969),
                        fontSize: 13,
                        fontWeight: '600',
                      ),
                    ),
                    SizedBox(
                      height: 5,
                    ),
                    NarFormLabelWidget(
                      label: progressMessage,
                      textAlign: TextAlign.center,
                      textColor: Color(0xff696969),
                      fontSize: 13,
                      fontWeight: '600',
                    ),

                  ],
                ),
              ),
            ));
          });
        });
  }

  Future<void> _fetchSupplierGanttData () async {
    jobEventItemSuppliersMapList.clear();
    for (var vendor in vendors) {
      if (vendor['label'] != null && vendor['label'].toString().isNotEmpty) {
        jobEventItemSuppliersMapList[vendor['label'].toString()] = [];
      }
    }
    List<String> vendorIds = vendors.map((e) => e['value'] as String).toList();
    QuerySnapshot<Map<String, dynamic>> snapshot = await FirebaseFirestore.instance
      .collection(appConfig.COLLECT_NEWARC_PROJECTS)
      .get();
    for (QueryDocumentSnapshot<Map<String, dynamic>> doc in snapshot.docs) {
      NewarcProject project = NewarcProject.fromDocument(doc.data(), doc.id);
      for (ProjectJob pj in project.projectJobs ?? []) {
        if (vendorIds.contains(pj.vendorUserId)) {
          jobEventItemSuppliersMapList[vendors.where((e) => e['value'] == pj.vendorUserId).first['label'].toString()]!.add(
            JobEventItem(
              project: project.name,
              title: pj.activity ?? "",
              duration: pj.timeline ?? "0",
              start: pj.hypotheticalStartDate.toString(),
              end: ((pj.hypotheticalStartDate ?? 0) + (int.tryParse(pj.timeline ?? "0") ?? 0)*24*60*60*1000).toString(),
            ),
          );
        }
      }
    }
  }
}

class JobEventItem {
  final String? project;
  final String title;
  final String duration;
  final String start;
  final String end;

  JobEventItem({
    this.project,
    required this.title,
    required this.duration,
    required this.start,
    required this.end,
  });
}



extension ProjectJobConversion on List<ProjectJob> {
  List<JobEventItem> toJobEventItems() {
    return map((projectJob) {
      DateTime startDate = (projectJob.hypotheticalStartDate != null &&
          projectJob.hypotheticalStartDate != 0)
          ? DateTime.fromMillisecondsSinceEpoch(projectJob.hypotheticalStartDate!)
          : DateTime.now();


      int timelineDays = int.tryParse(projectJob.timeline ?? "0") ?? 0;

      DateTime endDate = calculateBusinessEndDate(startDate, timelineDays);


      return JobEventItem(
        title: projectJob.activity ?? "",
        start: startDate.millisecondsSinceEpoch.toString(),
        end: endDate.millisecondsSinceEpoch.toString(),
        duration: timelineDays.toString(),
      );
    }).toList();
  }

  DateTime calculateBusinessEndDate(DateTime startDate, int timelineDays) {

    DateTime finalEndDate = _addBusinessDays(startDate,timelineDays );

    return finalEndDate;
  }

   DateTime _addBusinessDays(DateTime startDate, int totalBusinessDays) {
    DateTime currentDate = startDate;
    int addedDays = 1;
    while (addedDays < totalBusinessDays) {
      currentDate = currentDate.add(Duration(days: 1));
      if (currentDate.weekday != DateTime.saturday && currentDate.weekday != DateTime.sunday) {
        addedDays++;
      }
    }
    if ( currentDate.weekday == DateTime.saturday) {
      currentDate = currentDate.add(Duration(days: 2));
    } else if (currentDate.weekday == DateTime.sunday) {
      currentDate = currentDate.add(Duration(days: 1));
    }

    return currentDate;
  }

}






