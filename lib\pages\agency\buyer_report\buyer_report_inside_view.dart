import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import 'package:flutter_svg/svg.dart';
import 'package:newarc_platform/classes/agencyPersone.dart';
import 'package:newarc_platform/classes/reportAcquirente.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/tab/status_widget.dart';
import '../../../classes/agencyUser.dart';
import '../../../utils/buyerReportPDF.dart';
import '../../../utils/color_schema.dart';
import '../../../widget/UI/base_newarc_button.dart';
import '../../../widget/UI/base_newarc_popup.dart';
import '../../../widget/UI/buyer_report_data_insertion_popup.dart';
import '../../../widget/UI/checkbox.dart';
import '../../../widget/UI/custom_textformfield.dart';
import '../../../widget/UI/file-picker.dart';
import '../../../widget/UI/form-label.dart';
import 'package:newarc_platform/app_const.dart' as appConst;
import '../../../widget/UI/link.dart';
import '../../../widget/UI/tab/common_dropdown_widget.dart';
import '../../../widget/custom_icon_button.dart';

class BuyerReportInsideView extends StatefulWidget {
  final Function? updateViewCallback;
  final Map? projectArguments;
  final String? projectFirebaseId;
  final AgencyUser agencyUser;
  const BuyerReportInsideView({super.key,required this.updateViewCallback,required this.projectFirebaseId,this.projectArguments,required this.agencyUser});

  @override
  State<BuyerReportInsideView> createState() => _BuyerReportInsideViewState();
}

class _BuyerReportInsideViewState extends State<BuyerReportInsideView> {
  ReportAcquirente reportAcquirente = ReportAcquirente.empty();
  final Map<String, dynamic> characteristicsMap = Map.from(appConst.houseFeatures);

  bool isLoading = false;
  bool isShowRedErrorBorderColor = false;
  bool isShowRedErrorBorderFont = false;
  bool isShowRedErrorBorderCoverTemplateType = false;
  bool isShowRedErrorBorderPhotoTemplateType = false;
  bool isShowRedErrorBorderCover = false;
  bool isShowRedErrorBorderAgencyPerson = false;



  Map<String, ReportItem> reportItems = {};

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timestamp) async {
      await initialFetch(id: widget.projectFirebaseId!);
    });
  }

  Future<void> initialFetch({required String id}) async {
      setState(() {
        isLoading = true;
      });

    try {
      DocumentSnapshot<Map<String, dynamic>> collectionSnapshot;

      collectionSnapshot = await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_REPORT_ACQUIRENTE)
          .doc(id)
          .get();

      if (collectionSnapshot.data() != null) {
        reportAcquirente.copy(ReportAcquirente.fromDocument(collectionSnapshot.data()!, id),
        );

        // Caratteristiche
        characteristicsMap['Ascensore'] = reportAcquirente.elevator ?? false;
        characteristicsMap['Cantina'] = reportAcquirente.hasCantina ?? false;
        characteristicsMap['Terrazzo'] = reportAcquirente.terrace ?? false;
        characteristicsMap['Portineria'] = reportAcquirente.hasConcierge ?? false;
        characteristicsMap['Infissi ad alta efficienza'] = reportAcquirente.highEfficiencyFrames ?? false;
        characteristicsMap['Doppia esposizione'] = reportAcquirente.doubleEsposition ?? false;
        characteristicsMap['Tripla esposizione'] = reportAcquirente.tripleEsposition ?? false;
        characteristicsMap['Quadrupla esposizione'] = reportAcquirente.quadrupleEsposition ?? false;
        characteristicsMap['Risc. centralizzato'] = reportAcquirente.centralizedHeating ?? false;
        characteristicsMap['Risc. autonomo'] = reportAcquirente.autonomousHeating ?? false;
        characteristicsMap['Giardino privato'] = reportAcquirente.privateGarden ?? false;
        characteristicsMap['Giardino condominiale'] = reportAcquirente.sharedGarden ?? false;
        characteristicsMap['Stabile signorile'] = reportAcquirente.nobleBuilding ?? false;
        characteristicsMap['Stabile videosorvegliato'] = reportAcquirente.surveiledBuilding ?? false;
        characteristicsMap['Fibra ottica'] = reportAcquirente.fiber ?? false;
        characteristicsMap['Pred. condizionatore'] = reportAcquirente.airConditioning ?? false;
        characteristicsMap['Porta blindata'] = reportAcquirente.securityDoor ?? false;
        characteristicsMap['Impianto TV'] = reportAcquirente.tvStation ?? false;
        characteristicsMap['Pred. antifurto'] = reportAcquirente.alarm ?? false;
        characteristicsMap['Tapparelle motorizzate'] = reportAcquirente.motorizedSunblind ?? false;
        characteristicsMap['Tapparelle domotizzate'] = reportAcquirente.domotizedSunblind ?? false;
        characteristicsMap['Luci domotizzate'] = reportAcquirente.domotizedLights ?? false;
        characteristicsMap['Piano alto'] = reportAcquirente.highFloor ?? false;
        characteristicsMap['Vicinanza Metro'] = reportAcquirente.metroVicinity ?? false;
        characteristicsMap['Ampi balconi'] = reportAcquirente.bigBalconies ?? false;
        characteristicsMap['Grande zona living'] = reportAcquirente.bigLiving ?? false;
        characteristicsMap['Doppi servizi'] = reportAcquirente.doubleBathroom ?? false;
        characteristicsMap['Piscina'] = reportAcquirente.swimmingPool ?? false;
        characteristicsMap['Box o garage'] = reportAcquirente.hasGarage ?? false;
        characteristicsMap['Cabina armadio'] = reportAcquirente.walkInCloset ?? false;
        characteristicsMap['Fotovoltaico'] = reportAcquirente.solarPanel ?? false;

        reportItems =  buildReportItems(reportAcquirente);
      }
    } catch (e) {
      print(e);
    } finally {
        setState(() {
          isLoading = false;
        });
    }
  }

  Map<String, ReportItem> buildReportItems(ReportAcquirente project) {
    return {
      'Localizzazione': ReportItem(section: "localizzazione", isCompleted: project.localizzazione(), isIncluded: project.isLocalizzazioneIncludeInBuyerReport!),
      'Informazioni generali': ReportItem(section: "info-generali", isCompleted: project.info_generaliForBuyerReport(),isIncluded: project.isInformazioniGeneraliIncludeInBuyerReport!),
      'Caratteristiche': ReportItem(section: "caratteristiche", isCompleted: (characteristicsMap.values.any((value) => value == true)
          && ((reportAcquirente.constructionYear != null)&&(reportAcquirente.constructionYear != ""))
          && (reportAcquirente.externalEsposition.isNotEmpty)),isIncluded: project.isCaratteristicheInBuyerReport!),
      if(project.propertyType == "Appartamento") 'Informazioni sullo stabile': ReportItem(section: "info-sullo-stabile", isCompleted: project.info_sullo_stabileForBuyerReport(),isIncluded: project.isInformazioniSulloStabileIncludeInBuyerReport!),
      'Riscaldamento ed Energia': ReportItem(section: "riscaldamento-energetica", isCompleted: project.heating_energyForBuyerReport(),isIncluded: project.isHeatingAndEnergyInBuyerReport!),
      'Descrizione immobile': ReportItem(section: "descrizione", isCompleted: project.description?.isNotEmpty ?? false,isIncluded: project.isDescrizioneImmobileIncludeInBuyerReport!),
      'Descrizione del quartiere': ReportItem(section: "descrizione-del-quartiere", isCompleted: project.descriptionNeighborhood?.isNotEmpty ?? false,isIncluded: project.isDescrizioneDelQuartiereIncludeInBuyerReport!),
      'Prospetto costi': ReportItem(section: "prospetto-costi", isCompleted: project.prospettoCostiForBuyerReport(),isIncluded: project.isProspettoCostiInBuyerReport!),
      'Planimetria catastale': ReportItem(section: "planimetria", isCompleted: project.planimetry.isNotEmpty,isIncluded: project.isPlanimetriaCatastaleInBuyerReport!),
      'Fotografie interne': ReportItem(section: 'fotografie-interne', isCompleted: project.pictures.any((img) => img['type'] == 'internal'),isIncluded: project.isFotografieInterneInBuyerReport!),
      'Fotografie esterne': ReportItem(section: 'fotografie-esterne', isCompleted: project.pictures.any((img) => img['type'] == 'external'),isIncluded: project.isFotografieEsterneInBuyerReport!),
      // 'Virtual Tour': ReportItem(section: 'virtual-tour', isCompleted: project.reportVirtualTour?.isNotEmpty ?? false,isIncluded: project.isVirtualTourInBuyerReport!),
      if(!(project.objective != "Vendita" || project.buyerReportStatus != "Da ristrutturare")) 'Stima ristrutturazione': ReportItem(section: "stima-ristrutturazione", isCompleted: project.stimaRistrutturazione(),isIncluded: project.isStimaRistrutturazioneInBuyerReport!),
    };
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                      onTap: () {
                        widget.updateViewCallback!('report-acquirente', projectArguments: widget.projectArguments);
                      },
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SvgPicture.asset('assets/icons/arrow_left.svg',
                              height: 15,
                              color: Colors.black,
                          ),
                          SizedBox(width: 15,),
                          NarFormLabelWidget(
                            label: 'Tutti i report',
                            fontSize: 15,
                            fontWeight: '600',
                            textDecoration: TextDecoration.underline,
                            textColor: AppColor.black,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    alignment: Alignment.center,
                    child: NarFormLabelWidget(
                      label: isLoading ? "" : reportAcquirente.addressInfo!.toShortAddress(),
                      fontSize: 18,
                      fontWeight: '700',
                      textColor: Colors.black,
                    ),
                  ),
                ),
                Expanded(child: Container()),
              ]
          ),
        ),
    isLoading ?
    Positioned.fill(
      child: Align(
        alignment: Alignment.center,
        child: CircularProgressIndicator(
          color: Theme.of(context).primaryColor,
        ),
      ),
    )
        :
    Positioned(
      top: 25,
      left: 0,
      right: 0,
      bottom: 0,
      child: Padding(
        padding: EdgeInsets.only(top: 20),
        child: SingleChildScrollView(
          child: Column(
            children: [
              ListView(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                children: reportItems.entries.map((entry) {
                  return Container(
                    width: double.infinity,
                    height: 60,
                    margin: EdgeInsets.only(bottom: 10),
                    padding: EdgeInsets.symmetric(horizontal: 10),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: entry.value.isCompleted ? Colors.transparent : Color(0xFFF4F4F4),
                      border: Border.all(color: entry.value.isCompleted ?  Color(0xFFC3C9CF) : Colors.transparent,
                      )
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 1,
                          child: NarFormLabelWidget(
                            label: entry.key,
                            fontSize: 16,
                            fontWeight: '700',
                            textColor: entry.value.isCompleted ? AppColor.black : Color(0xFF6A6A6A),
                          ),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            SizedBox(
                              child: StatusWidget(
                                status: entry.value.isCompleted ? "compilato" : "non compilato",
                                statusColor: entry.value.isCompleted ? AppColor.successGreenColor : Color(0xFFCFCFCF),
                              ),
                              width: 110,
                            ),
                            SizedBox(width:  35),
                            (entry.value.isCompleted && entry.value.section != "localizzazione" && entry.value.section != "info-generali") ?

                            SizedBox(
                              width: 200,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Transform.scale(
                                    scale: 0.8,
                                    child: Switch(
                                      value: entry.value.isIncluded,
                                      activeColor: AppColor.white,
                                      activeTrackColor: Theme.of(context).primaryColor,
                                      onChanged: (bool value) {
                                        onChangeOfSwitch(value: value,currentView: entry.value.section);
                                        setState(() {
                                          entry.value.isIncluded = value;
                                        });

                                      },
                                    ),
                                  ),
                                  SizedBox(
                                    width: 5,
                                  ),
                                  NarFormLabelWidget(
                                    label: 'Includi nel report',
                                    fontSize: 14,
                                    fontWeight: '600',
                                    textColor: AppColor.black,
                                  ),
                                ],
                              ),
                            ) : SizedBox(width:  200),
                            SizedBox(width: 35),
                            SizedBox(
                              width: 120,
                              child: CustomIconButton(
                                label: entry.value.isCompleted ? "Modifica" : "Compila",
                                textStyle: TextStyle(
                                  color: entry.value.isCompleted ? Color(0xFF6D6D6D) : AppColor.white,
                                  fontSize: 13,
                                  fontWeight: FontWeight.w600,
                                  letterSpacing: 1.0
                                ),
                                boarderColor: entry.value.isCompleted ? Color(0xFFD8D8D8) : Colors.transparent,
                                width: 120,
                                height: 30,
                                leadingIcon: false,
                                icon: entry.value.isCompleted ? Padding(
                                  padding: const EdgeInsets.only(left: 5.0),
                                  child: SvgPicture.asset("assets/icons/edit.svg",color: Color(0xFF6D6D6D),height: 15,),
                                ) : SizedBox.shrink(),
                                color: entry.value.isCompleted ? Colors.transparent : Color(0xFF6D6D6D),
                                function: () async{
                                  await showDialog(
                                  context: context,
                                  barrierDismissible: false,
                                  builder: (BuildContext context) {
                                    return Center(
                                      child: BuyerReportDataInsertionPopup(
                                        isFromEdit: true,
                                        procedureStep: entry.value.section,
                                        agencyUser: widget.agencyUser,
                                        project: reportAcquirente,
                                        onClose: (String id) {
                                          if(id.isNotEmpty){
                                            initialFetch(id: id);
                                          }
                                        },
                                      ),
                                    );
                                  },
                                  );
                                },
                                borderRadius: 100,
                              ),
                            ),
                          ],
                        )
                      ],
                    ),
                  );
                }).toList(),
              ),
              SizedBox(height: 30,),
              Container(
                height: 505,
                width: double.infinity,
                margin: const EdgeInsets.symmetric(horizontal: 16),
                padding: const EdgeInsets.symmetric(vertical: 10),
                decoration: BoxDecoration(
                  color: Color(0xFFE0ECF9),
                  borderRadius: BorderRadius.circular(10)
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(height: 25,),
                    NarFormLabelWidget(
                      label: "Il tuo Report Acquirente",
                      fontSize: 22,
                      fontWeight: '700',
                      textColor: Theme.of(context).primaryColor,
                    ),
                    SizedBox(height: 25,),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 25),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          //----Select Color
                          Expanded(
                            child: _reportCart(
                                onTapButton: (){
                              showSelectColorDialog();
                            },title: "Colore in evidenza",
                                child: Container(
                                  height: 50,
                                  width: 50,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Color(int.tryParse(reportAcquirente.buyerReportColor ?? "0xFFB3B3B3") ?? 0xFFB3B3B3),
                                  ),
                                ),
                              borderColor: isShowRedErrorBorderColor  ? AppColor.redColor : Colors.transparent
                            ),
                          ),
                          SizedBox(width: 10,),
                          //----Select Image
                          Expanded(
                            child: _reportCart(onTapButton: (){
                              showSelectImageDialog();
                            },title: "Foto Copertina",
                                child:  Container(
                                  height: 82,
                                  width: 130,
                                  decoration: BoxDecoration(
                                      color: reportAcquirente.buyerReportCoverImage?.isNotEmpty ?? false ?  null :Color(0xFFE6E6E6),
                                      borderRadius: BorderRadius.circular(8)
                                  ),
                                  child:  (reportAcquirente.buyerReportCoverImage?.isNotEmpty ?? false) ? NarFilePickerWidget(
                                    allowMultiple: false,
                                    filesToDisplayInList: 0,
                                    removeButton: false,
                                    isDownloadable: false,
                                    containerMarginLeft: 0,
                                    removeButtonText: 'Elimina',
                                    removeButtonTextColor: Color(0xff797979),
                                    uploadButtonPosition: 'back',
                                    showMoreButtonText: '+ espandi',
                                    actionButtonPosition: 'bottom',
                                    displayFormat: 'inline-widget',
                                    containerWidth: 130,
                                    containerHeight: 80,
                                    containerBorderRadius: 8,
                                    borderRadius: 8,
                                    fontSize: 11,
                                    fontWeight: '600',
                                    text: 'Carica Cover',
                                    borderSideColor: Theme.of(context).primaryColor,
                                    hoverColor: Color.fromRGBO(133, 133, 133, 1),
                                    allFiles: [reportAcquirente.buyerReportCoverImage?["file"]],
                                    pageContext: context,
                                    storageDirectory: "${appConfig.COLLECT_REPORT_ACQUIRENTE}/${reportAcquirente.id}/",
                                    removeExistingOnChange: false,
                                    hasPreview: false,
                                    imageContainerPadding: EdgeInsets.zero,
                                    progressMessage: [''],
                                    notAccent: true,
                                    showTitle: false,
                                    onUploadCompleted: () {
                                      if (mounted) {
                                        setState(() {});
                                      }
                                    },
                                  ) : SizedBox(
                                    height: 78,
                                    width: 130,
                                  ),
                                ),
                                borderColor: isShowRedErrorBorderCover  ? AppColor.redColor : Colors.transparent

                            ),
                          ),
                          SizedBox(width: 10,),
                          //----Select Template Cover
                          Expanded(
                            child: _reportCart(onTapButton: (){
                              showSelectTemplateCoverDialog(context: context,reportAcquirente: reportAcquirente);
                            },title: "Template Copertina",
                                child:  (reportAcquirente.coverTemplateType?.isNotEmpty ?? false) ?  Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        NarFormLabelWidget(
                                          label: "Template ${reportAcquirente.coverTemplateType?.toCapitalized() ?? ""}",
                                          fontSize: 12,
                                          fontWeight: '700',
                                        ),
                                        SizedBox(height: 2,),
                                        NarFormLabelWidget(
                                          label: " • ${reportAcquirente.coverTemplateType == "classic" ? "Immagine metà pagina" : "Immagine piena"}",
                                          fontSize: 12,
                                          fontWeight: '500',
                                        ),
                                        NarFormLabelWidget(
                                          label: " • ${reportAcquirente.coverTemplateType == "classic" ? "Titoli a sinistra" : "Titoli centrati"}",
                                          fontSize: 12,
                                          fontWeight: '500',
                                        ),
                                      ],
                                    ),

                                    Image.asset(reportAcquirente.coverTemplateType == "classic" ? "assets/icons/cover_classic.png" : "assets/icons/cover_full.png"),

                                  ],
                                ) : SizedBox(),
                                borderColor: isShowRedErrorBorderCoverTemplateType  ? AppColor.redColor : Colors.transparent

                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 25,),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 25),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          //----Select Template fotografie
                          Expanded(
                            child: _reportCart(onTapButton: (){
                              showSelectTemplatePhotoDialog(context: context,reportAcquirente: reportAcquirente);
                            },title: "Template fotografie",
                                child:  (reportAcquirente.photoTemplateType?.isNotEmpty ?? false) ?  Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        NarFormLabelWidget(
                                          label: "Template ${reportAcquirente.photoTemplateType?.toCapitalized() ?? ""}",
                                          fontSize: 12,
                                          fontWeight: '700',
                                        ),
                                        SizedBox(height: 2,),
                                        NarFormLabelWidget(
                                          label: " • ${reportAcquirente.photoTemplateType == "wide" ? "Immagini grandi" :"Immagini piccole"}",
                                          fontSize: 12,
                                          fontWeight: '500',
                                        ),
                                        NarFormLabelWidget(
                                          label: " • ${reportAcquirente.photoTemplateType == "wide" ? "1 immagine/pagina" :"Tutte in una pagina"}",
                                          fontSize: 12,
                                          fontWeight: '500',
                                        ),
                                      ],
                                    ),
                                    Image.asset( reportAcquirente.photoTemplateType == "wide" ? "assets/icons/photo_template_wide.png":  "assets/icons/photo_template_short.png"),

                                  ],
                                ) : SizedBox(),
                                borderColor: isShowRedErrorBorderPhotoTemplateType  ? AppColor.redColor : Colors.transparent

                            ),
                          ),
                          SizedBox(width: 10,),
                          //----Select Font
                          Expanded(
                            child: _reportCart(
                                onTapButton: (){
                                  showSelectFontDialog(context: context,reportAcquirente: reportAcquirente);
                            },title: "Font",
                                child: (reportAcquirente.fontType?.isNotEmpty ?? false) ? Padding(
                                  padding: const EdgeInsets.only(top: 20),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      NarFormLabelWidget(
                                        label: "Stile selezionato: ",
                                        fontSize: 14,
                                        fontWeight: '500',
                                        textColor: Color(0xFF7D7D7D),
                                      ),
                                      Text(
                                        "${reportAcquirente.fontType == "raleway" ? "Minimal" : reportAcquirente.fontType == "tinos" ? "Elegant" : ""}",
                                        style: TextStyle(
                                          fontSize: 14,
                                          fontFamily: reportAcquirente.fontType == "raleway" ? "Raleway-600" : reportAcquirente.fontType == "tinos" ? "Tinos-700" : ""
                                        ),
                                      )
                                    ],
                                  ),
                                ) : SizedBox(),
                              borderColor: isShowRedErrorBorderFont  ? AppColor.redColor : Colors.transparent
                            ),
                          ),
                          SizedBox(width: 10,),
                          //---- Select Agency person
                          Expanded(
                            child: _reportCart(
                                onTapButton: (){
                                  showAgencyPersonDialog(agencyPesonaId: reportAcquirente.buyerReportAgencyPersonId ?? "");
                                },title: "Agente Immobiliare",
                                child: FutureBuilder<Widget>(
                                    future: fetchAgencyPersonaById(id: reportAcquirente.buyerReportAgencyPersonId ?? ""),
                                    builder: (context,snapshot){
                                      if(snapshot.hasData){
                                        return snapshot.data!;
                                      }else if (snapshot.hasError) {
                                        return Container(
                                          width: 30,
                                          height: 30,
                                          alignment: Alignment.center,
                                        );
                                      }
                                      return Center(
                                        child: CircularProgressIndicator(
                                          color: Theme.of(context).primaryColor,
                                        ),
                                      );
                                    }),
                                borderColor: isShowRedErrorBorderAgencyPerson  ? AppColor.redColor : Colors.transparent
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 25,),
                    ElevatedButton(
                        onPressed: ()async{
                          if(reportAcquirente.isGenerateButtonEnable()){
                            ValueNotifier<double> progress = ValueNotifier(0);
                            showGenerateReportDialog(progress: progress);
                            await downloadBuyerReportPDF(reportAcquirente: reportAcquirente, progress: progress);
                            Navigator.pop(context);
                          }else{
                            isShowRedErrorBorderColor = reportAcquirente.buyerReportColor?.isEmpty ?? true;
                            isShowRedErrorBorderCover = reportAcquirente.buyerReportCoverImage?.isEmpty ?? true;
                            isShowRedErrorBorderAgencyPerson = reportAcquirente.buyerReportAgencyPersonId?.isEmpty ?? true;
                            isShowRedErrorBorderPhotoTemplateType = reportAcquirente.photoTemplateType?.isEmpty ?? true;
                            isShowRedErrorBorderFont = reportAcquirente.fontType?.isEmpty ?? true;
                            isShowRedErrorBorderCoverTemplateType = reportAcquirente.coverTemplateType?.isEmpty ?? true;
                            setState(() {});
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          elevation: 0,
                          fixedSize: Size(190, 45),
                          backgroundColor: Theme.of(context).primaryColor.withOpacity(reportAcquirente.isGenerateButtonEnable() ? 1 : 0.6),
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                        ),
                        child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                          NarFormLabelWidget(
                            label: 'Genera Report',
                            textColor: Colors.white.withOpacity(reportAcquirente.isGenerateButtonEnable() ? 1 : 0.6),
                            fontWeight: '600',
                            letterSpacing: .5,
                            fontSize: 15,
                          ),
                          SvgPicture.asset(
                            'assets/icons/pdf_icon.svg',
                            height: 16,
                            color: Colors.white.withOpacity(reportAcquirente.isGenerateButtonEnable() ? 1 : 0.6),
                          ),
                        ]))
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    )
    ],
    );


  }


  Future<Widget> fetchAgencyPersonaById({required String id})async{

    if(id.isEmpty) return Container();

    DocumentSnapshot<Map<String, dynamic>> collectionSnapshotQuery =  await FirebaseFirestore.instance
        .collection(appConfig.COLLECT_AGENCY_PERSONA).doc(id).get();


    if (collectionSnapshotQuery.exists) {
        try {
          AgencyPersone _tmp = AgencyPersone.fromDocument(collectionSnapshotQuery.data()!, collectionSnapshotQuery.id);

          return Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              NarFilePickerWidget(
                allowMultiple: false,
                filesToDisplayInList: 0,
                removeButton: false,
                isDownloadable: false,
                removeButtonText: 'Elimina',
                removeButtonTextColor: AppColor.black,
                uploadButtonPosition: 'back',
                showMoreButtonText: '+ espandi',
                actionButtonPosition: 'bottom',
                displayFormat: 'inline-widget',
                containerWidth: 50,
                containerHeight: 50,
                containerBorderRadius: 100,
                borderRadius: 7,
                fontSize: 11,
                fontWeight: '600',
                text: 'Carica Profile Pic',
                borderSideColor: Theme.of(context).primaryColor,
                hoverColor: Color.fromRGBO(133, 133, 133, 1),
                allFiles: _tmp.profilePicture,
                pageContext: context,
                storageDirectory: "agencyPersona/profile/${_tmp.firebaseId}",
                removeExistingOnChange: true,
                hasPreview: false,
                progressMessage: [''],
                notAccent: true,
                showTitle: false,
                onUploadCompleted: () {
                  if (mounted) {
                    setState(() {});
                  }
                },
              ),
              SizedBox(width: 20,),
              NarFormLabelWidget(
                label: "${_tmp.name} ${_tmp.surname}",
                fontSize: 14,
                fontWeight: '600',
              ),
            ],
          );

        } catch (e) {
          print("ERROR fetch Agency Persone  By Id---> $e");
        }

    }

    return Container();
  }

  Widget _reportCart({required String title,required Function onTapButton,required Widget child,Color? btnColor,Color? textColor,required Color borderColor}){
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: AppColor.white,
        border: Border.all(color: borderColor)
      ),
      padding: EdgeInsets.all(10),
      height: 150,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              NarFormLabelWidget(
                label: title,
                fontSize: 15,
                fontWeight: '700',
              ),
              BaseNewarcButton(
                color: btnColor ?? Theme.of(context).primaryColor,
                textColor: textColor ?? AppColor.white,
                buttonText: "Seleziona",
                fontWeight: '600',
                fontSize: 13,
                height: 28,
                borderRadius: 100,
                onPressed: onTapButton,
              )

            ],
          ),
          SizedBox(height: 15,),
          Center(child: child)
        ],
      ),
    );
  }

  Future<void> showSelectColorDialog() async {
    List<String> formErrorMessage = [];
    String selectedColor = (reportAcquirente.buyerReportColor?.isNotEmpty ?? false)
        ? reportAcquirente.buyerReportColor!
        : "0xFFB3B3B3";
    await showDialog(
        context: context,
        builder: (BuildContext _context) {
          return StatefulBuilder(builder: (__context, _setState) {
            return Center(
                child: BaseNewarcPopup(
                  title: "Colore in evidenza",
                  buttonText: "Seleziona",
                  formErrorMessage: formErrorMessage,
                  onPressed: () async {
                    _setState((){
                      formErrorMessage.clear();
                      formErrorMessage.add("Salvataggio in corso...");
                    });
                    try {
                      setState(() {
                        reportAcquirente.buyerReportColor = selectedColor;
                      });
                      await FirebaseFirestore.instance
                          .collection(appConfig.COLLECT_REPORT_ACQUIRENTE)
                          .doc(reportAcquirente.id)
                          .set(reportAcquirente.toMap());

                        setState(() {
                          isShowRedErrorBorderColor = false;
                        });
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Salvato");
                      });

                    } catch (e) {
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Si è verificato un errore.");
                      });
                      log("Error While Selecting Color  -----> ${e.toString()}");
                    }
                  },
                  buttonColor: Theme.of(context).primaryColor,
                  column: Container(
                    width: 600,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        NarFormLabelWidget(
                          label: 'Seleziona il colore più simile a quello del tuo logo',
                          fontWeight: '500',
                          fontSize: 18,
                        ),
                        SizedBox(height: 25),
                        SizedBox(
                          width: 400,
                          child: HueRingPicker(
                            pickerColor: Color(int.tryParse(selectedColor) ?? 0xFFB3B3B3),
                            onColorChanged: (color) {
                              String hex = color.toHexString(toUpperCase: true).padLeft(8, '0');
                              String finalHex = hex.substring(2).toUpperCase();
                              _setState(() {
                                selectedColor = "0xFF${finalHex}";
                              });
                            },
                            portraitOnly: true,
                            enableAlpha: false,
                            displayThumbColor: true,
                            pickerAreaBorderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        SizedBox(height: 25),
                      ],
                    ),
                  ),
                ));
          });
        });
  }

  Future<void> showSelectImageDialog() async {
    List<String> formErrorMessage = [];
    Map? selectedImage;
    await showDialog(
        context: context,
        builder: (BuildContext _context) {
          return StatefulBuilder(builder: (__context, _setState) {
            selectedImage ??= (reportAcquirente.buyerReportCoverImage);
            return Container(
                width: MediaQuery.of(context).size.width * 0.99,
                height: MediaQuery.of(context).size.height * 0.99,
                child: BaseNewarcPopup(
                  title: "Foto Copertina",
                  buttonText: "Seleziona",
                  formErrorMessage: formErrorMessage,
                  borderRadius: 0.0,
                  horizontalMargin: 0,
                  onPressed: () async {
                    _setState((){
                      formErrorMessage.clear();
                      formErrorMessage.add("Salvataggio in corso...");
                    });
                    try {
                      setState(() {
                        reportAcquirente.buyerReportCoverImage = selectedImage;
                      });
                      await FirebaseFirestore.instance
                          .collection(appConfig.COLLECT_REPORT_ACQUIRENTE)
                          .doc(reportAcquirente.id)
                          .set(reportAcquirente.toMap());
                        setState(() {
                          isShowRedErrorBorderCover = false;
                        });
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Salvato");
                      });

                    } catch (e) {
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Si è verificato un errore.");
                      });
                      log("Error While Selecting Image  -----> ${e.toString()}");
                    }
                  },
                  buttonColor: Theme.of(context).primaryColor,
                  column: SizedBox(
                    height: MediaQuery.of(context).size.height * 0.80,
                    width: MediaQuery.of(context).size.width * 0.99,
                    child: Column(
                      children: [
                        NarFormLabelWidget(
                          label: "Seleziona una foto tra quelle caricate",
                          fontSize: 18,
                          fontWeight: '500',
                        ),
                        SizedBox(height: 30,),
                        Wrap(
                          spacing: 25,
                          runSpacing: 25,
                          crossAxisAlignment: WrapCrossAlignment.start,
                          runAlignment: WrapAlignment.start,
                          alignment: WrapAlignment.start,
                          children: reportAcquirente.pictures.map<Widget>((picture) {
                            bool isSelectedImage = mapEquals(selectedImage, picture);
                            return SizedBox(
                              width: 145, // Match the fixed container width
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10),
                                      border: Border.all(
                                        color: isSelectedImage ? AppColor.black : Colors.transparent,
                                        width: 4,
                                      ),
                                    ),
                                    clipBehavior: Clip.hardEdge,
                                    child: NarFilePickerWidget(
                                      allowMultiple: false,
                                      filesToDisplayInList: 0,
                                      onTapOfImage: () {
                                        _setState(() {
                                          selectedImage = picture;
                                        });
                                      },
                                      removeButton: false,
                                      isDownloadable: false,
                                      containerMarginLeft: 0,
                                      removeButtonText: 'Elimina',
                                      removeButtonTextColor: const Color(0xff797979),
                                      uploadButtonPosition: 'back',
                                      showMoreButtonText: '+ espandi',
                                      actionButtonPosition: 'bottom',
                                      displayFormat: 'inline-widget',
                                      containerWidth: 145,
                                      containerHeight: 145,
                                      containerBorderRadius: 10,
                                      borderRadius: 10,
                                      fontSize: 11,
                                      fontWeight: '600',
                                      text: 'Carica Cover',
                                      borderSideColor: Theme.of(context).primaryColor,
                                      hoverColor: const Color.fromRGBO(133, 133, 133, 1),
                                      allFiles: [picture["file"]],
                                      pageContext: context,
                                      storageDirectory: "${appConfig.COLLECT_REPORT_ACQUIRENTE}/${reportAcquirente.id}/",
                                      removeExistingOnChange: false,
                                      hasPreview: false,
                                      imageContainerPadding: EdgeInsets.zero,
                                      progressMessage: const [''],
                                      notAccent: true,
                                      showTitle: false,
                                      onUploadCompleted: () {
                                        if (mounted) {
                                          _setState(() {});
                                        }
                                      },
                                    ),
                                  ),
                                  const SizedBox(height: 2),
                                  Padding(
                                    padding: const EdgeInsets.only(left: 5),
                                    child: NarFormLabelWidget(
                                      label: picture["tag"],
                                      fontSize: 12,
                                      fontWeight: '600',
                                      textColor: const Color(0xFF717171),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }).toList(),
                        )
                      ],
                    ),
                  ),
                ));
          });
        });
  }

  Future<void> showGenerateReportDialog({required ValueNotifier<double> progress}) async {

    await showDialog(
        context: context,
        barrierColor: Colors.black54,
        builder: (BuildContext _context) {
          return StatefulBuilder(builder: (__context, _setState) {
            return Center(
                child: BaseNewarcPopup(
                  title: "Il tuo Report Acquirente",
                  noButton: true,
                  onPressed: () async {
                    try {

                    } catch (e) {
                      _setState((){

                      });
                      log("Error While Generating PDF  -----> ${e.toString()}");
                    }
                  },
                  buttonColor: Theme.of(context).primaryColor,
                  column: ValueListenableBuilder<double>(
                  valueListenable: progress,
                  builder: (context, value, _) {
                    return Container(
                      width: MediaQuery.of(context).size.width * 0.4,
                      height: MediaQuery.of(context).size.height * 0.4,
                      padding: const EdgeInsets.all(10),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          value == 0
                              ? const SizedBox()
                              : Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              NarFormLabelWidget(
                                label: '${(value * 100).toStringAsFixed(0)}%',
                                fontSize: 19,
                                fontWeight: '500',
                                textColor: const Color(0xFF7C7C7C),
                              ),
                              const SizedBox(height: 30),
                               NarFormLabelWidget(
                                label: "Genero report...",
                                fontSize: 25,
                                fontWeight: '500',
                              ),
                            ],
                          ),
                        ],
                      ),
                    );
                  },
                ),
                ));
          });
        });
  }

  Future<void> showAgencyPersonDialog({required String agencyPesonaId}) async {
    TextEditingController agencyNameController = TextEditingController(text: agencyPesonaId);
    List<String> formErrorMessage = [];
    Future<void>? fetchAgencyPersoneFuture;

    await showDialog(
        context: context,
        builder: (BuildContext _context) {
          fetchAgencyPersoneFuture ??= fetchAgencyPersone();
          return StatefulBuilder(builder: (__context, _setState) {
            refreshAgencyPersona(){
              _setState((){
                fetchAgencyPersoneFuture = fetchAgencyPersone();
              });
            }
            return Center(
                child: BaseNewarcPopup(
                  title: "Agente Immobiliare",
                  buttonText: "Seleziona",
                  formErrorMessage: formErrorMessage,
                  onPressed: () async {
                    _setState((){
                      formErrorMessage.clear();
                      formErrorMessage.add("Salvataggio in corso...");
                    });
                    try {
                      setState(() {
                        reportAcquirente.buyerReportAgencyPersonId = agencyNameController.text;
                      });
                      await FirebaseFirestore.instance
                          .collection(appConfig.COLLECT_REPORT_ACQUIRENTE)
                          .doc(reportAcquirente.id)
                          .set(reportAcquirente.toMap());
                        setState(() {
                          isShowRedErrorBorderAgencyPerson = false;
                        });
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Salvato");
                      });

                    } catch (e) {
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Si è verificato un errore.");
                      });
                      log("Error While Adding Selecting Agency Persona -----> ${e.toString()}");
                    }
                  },
                  buttonColor: Theme.of(context).primaryColor,
                  column: SizedBox(
                    width: 500,
                    child: Column(
                      children: [
                        SizedBox(
                          width: 300,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              NarFormLabelWidget(
                                label: "Seleziona un agente",
                                fontSize: 14,
                                fontWeight: '600',
                                textColor: AppColor.greyColor,
                              ),
                              NarLinkWidget(
                                fontSize: 12,
                                fontWeight: '600',
                                textColor: Theme.of(context).primaryColor,
                                text: 'Aggiungi nuovo',
                                textDecoration: TextDecoration.underline,
                                onClick: () async{
                                  String id = await FirebaseFirestore
                                      .instance
                                      .collection(appConfig.COLLECT_AGENCY_PERSONA).doc().id;
                                  AgencyPersone per = AgencyPersone.empty();
                                  per.agencyId = reportAcquirente.agencyId;
                                  per.firebaseId = id;
                                  showAddPersonaDialog(context: context,persone: per,onAdded: refreshAgencyPersona);
                                },
                              )
                            ],
                          ),
                        ),
                        SizedBox(height: 10),
                        Container(
                          width: 300,
                          height: 51,
                          child: FutureBuilder<List>(
                              future: fetchAgencyPersone(),
                              builder: (context, snapshot) {
                                if (snapshot.hasData) {
                                  return DropdownButtonFormField<String>(
                                    isExpanded: true,
                                    value: agencyNameController.text.isNotEmpty
                                        ? agencyNameController.text
                                        : null,
                                    icon: Padding(
                                      padding: EdgeInsets.only(right: 8),
                                      child: SvgPicture.asset(
                                        'assets/icons/arrow_down.svg',
                                        width: 12,
                                        color: Color(0xff7e7e7e),
                                      ),
                                    ),
                                    style: TextStyle(
                                      overflow: TextOverflow.ellipsis,
                                      color: Colors.black,
                                      fontSize: 12.0,
                                      fontWeight: FontWeight.bold,
                                      fontStyle: FontStyle.normal,
                                    ),
                                    borderRadius: BorderRadius.circular(8),
                                    decoration: InputDecoration(
                                      border: OutlineInputBorder(
                                        borderRadius:
                                        BorderRadius.all(Radius.circular(8)),
                                        borderSide: BorderSide(
                                          color: Color.fromRGBO(227, 227, 227, 1),
                                          width: 1,
                                        ),
                                      ),
                                      hintStyle: TextStyle(
                                        color: Colors.grey,
                                        fontSize: 15.0,
                                        fontWeight: FontWeight.w800,
                                        fontStyle: FontStyle.normal,
                                        letterSpacing: 0,
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius:
                                        BorderRadius.all(Radius.circular(8)),
                                        borderSide: BorderSide(
                                          color: Color.fromRGBO(227, 227, 227, 1),
                                          width: 1,
                                        ),
                                      ),
                                      contentPadding: EdgeInsets.symmetric(
                                          horizontal: 12, vertical: 8),
                                      fillColor: Colors.white,
                                    ),
                                    onChanged: (String? value) {
                                      setState(() {
                                        agencyNameController.text = value ?? "";
                                      });
                                    },
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return "Required!";
                                      }
                                      return null;
                                    },
                                    dropdownColor: Colors.white,
                                    items: snapshot.data
                                        ?.map<DropdownMenuItem<String>>((item) {
                                      return DropdownMenuItem<String>(
                                        value: item['value'],
                                        child: NarFormLabelWidget(
                                          label: item['label']!,
                                          textColor: Colors.black,
                                          fontSize: 14,
                                          fontWeight: '600',
                                        ),
                                      );
                                    }).toList(),
                                  );
                                } else if (snapshot.hasError) {
                                  return Container(
                                    width: 30,
                                    height: 30,
                                    alignment: Alignment.center,
                                  );
                                }
                                return Center(
                                  child: CircularProgressIndicator(
                                    color: Theme.of(context).primaryColor,
                                  ),
                                );
                              }),
                        ),
                      ],
                    ),
                  ),
                ));
          });
        });
  }

  Future<List> fetchAgencyPersone() async {
    try {
      List<AgencyPersone> _agencyPersone = [];
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = FirebaseFirestore.instance
          .collection(appConfig.COLLECT_AGENCY_PERSONA);

      collectionSnapshot = await collectionSnapshotQuery
          .where('agencyId', isEqualTo: reportAcquirente.agencyId)
          .orderBy('insertTimestamp', descending: true)
          .get();

      if (collectionSnapshot.docs.length > 0) {
        for (var element in collectionSnapshot.docs) {
          try {
            AgencyPersone _tmp =
            AgencyPersone.fromDocument(
                element.data(), element.id);
            _agencyPersone.add(_tmp);
          } catch (e) {
            print("ERROR fetchAgencyPersone ---> $e");
          }
        }
      }


      if (_agencyPersone.length > 0) {
        return _agencyPersone.map((e) {
          return {'value': e.firebaseId, 'label': "${e.name} ${e.surname}"};
        }).toList();
      } else {
        return [
          {'value': '', 'label': ''}
        ];
      }
    } catch (e, s) {
      print('error: $e');
      print(s);
      return [
        {'value': '', 'label': ''}
      ];
    }
  }

  Future<void> onChangeOfSwitch({required bool value,required String currentView})async{
    if (currentView == 'localizzazione') {
     setState(() {
       reportAcquirente.isLocalizzazioneIncludeInBuyerReport = value;
     });
    } else if (currentView == 'info-generali') {
      setState(() {
        reportAcquirente.isInformazioniGeneraliIncludeInBuyerReport = value;
      });
    }else if (currentView == 'info-sullo-stabile') {
      setState(() {
        reportAcquirente.isInformazioniSulloStabileIncludeInBuyerReport = value;
      });
    } else if (currentView == 'descrizione') {
      setState(() {
        reportAcquirente.isDescrizioneImmobileIncludeInBuyerReport = value;
      });
    }  else if (currentView == 'descrizione-del-quartiere') {
      setState(() {
        reportAcquirente.isDescrizioneDelQuartiereIncludeInBuyerReport = value;
      });
    } else if (currentView == 'caratteristiche') {
      setState(() {
        reportAcquirente.isCaratteristicheInBuyerReport = value;
      });
    } else if (currentView == 'prospetto-costi') {
      setState(() {
        reportAcquirente.isProspettoCostiInBuyerReport = value;
      });
    } else if (currentView == 'planimetria') {
      setState(() {
        reportAcquirente.isPlanimetriaCatastaleInBuyerReport = value;
      });
    } else if (currentView == 'fotografie-interne') {
      setState(() {
        reportAcquirente.isFotografieInterneInBuyerReport = value;
      });
    }else if (currentView == 'fotografie-esterne') {
      setState(() {
        reportAcquirente.isFotografieEsterneInBuyerReport = value;
      });
    } else if (currentView == 'virtual-tour') {
      setState(() {
        reportAcquirente.isVirtualTourInBuyerReport = value;
      });
    }else if (currentView == 'stima-ristrutturazione') {
      setState(() {
        reportAcquirente.isStimaRistrutturazioneInBuyerReport = value;
      });
    }else if (currentView == 'riscaldamento-energetica') {
      setState(() {
        reportAcquirente.isHeatingAndEnergyInBuyerReport = value;
      });
    }
    await FirebaseFirestore.instance.collection(appConfig.COLLECT_REPORT_ACQUIRENTE).doc(reportAcquirente.id).set(reportAcquirente.toMap());
  }

  void showAddPersonaDialog({required BuildContext context,AgencyPersone? persone,required VoidCallback onAdded}) async {
    AgencyPersone? newPersone = AgencyPersone.empty();
    newPersone = persone;
    TextEditingController firstName = TextEditingController(text: newPersone?.name ?? "");
    TextEditingController lastName = TextEditingController(text: newPersone?.surname ?? "");
    TextEditingController phoneNumber = TextEditingController(text: newPersone?.phone ?? "");
    List<String> formErrorMessage = [];
    return showDialog(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context,_setState){
            return Center(
              child: BaseNewarcPopup(
                  buttonColor: Theme.of(context).primaryColor,
                  title: "Aggiungi persona",
                  buttonText: "Aggiungi",
                  formErrorMessage: formErrorMessage,
                  onPressed: () async {
                    _setState((){
                      formErrorMessage.clear();
                      formErrorMessage.add("Salvataggio in corso...");
                    });
                    try{
                      AgencyPersone newPer = AgencyPersone.empty();
                      newPer.name = firstName.text.trim()
                          .split(' ')
                          .map((word) => word.isNotEmpty
                          ? '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}'
                          : '')
                          .join(' ');
                      newPer.surname = lastName.text.trim()
                          .split(' ')
                          .map((word) => word.isNotEmpty
                          ? '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}'
                          : '')
                          .join(' ');
                      newPer.agencyId = reportAcquirente.agencyId;
                      newPer.insertTimestamp = DateTime.now().millisecondsSinceEpoch;
                      newPer.phone = phoneNumber.text;
                      if(newPersone?.profilePicture?.isNotEmpty ?? false){
                        newPer.profilePicturePath = {
                          "location": "agencyPersona/profile/${persone!.firebaseId}",
                          "filename": newPersone!.profilePicture![0],
                        };
                      }

                      await FirebaseFirestore
                          .instance
                          .collection(appConfig.COLLECT_AGENCY_PERSONA)
                          .doc(persone!.firebaseId)
                          .set(newPer.toMap());
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Salvato");
                      });
                      onAdded();
                    }catch(e,s){
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Si è verificato un errore.");
                      });
                      log("---------- ERROR While Agency Persone ------> ${e.toString()}");
                    }
                  },
                  column: Container(
                    width: 400,
                    padding: EdgeInsets.symmetric(vertical: 25),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 300,
                          child: CustomTextFormField(
                            isExpanded: false,
                            textCapitalization: TextCapitalization.words,
                            label: "Nome",
                            minLines: 1,
                            controller: firstName,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return "Required!";
                              }
                              return null;
                            },
                          ),
                        ),
                        SizedBox(height: 10,),
                        SizedBox(
                          width: 300,
                          child: CustomTextFormField(
                            isExpanded: false,
                            label: "Cognome",
                            minLines: 1,
                            textCapitalization: TextCapitalization.words,
                            controller: lastName,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return "Required!";
                              }
                              return null;
                            },
                          ),
                        ),
                        SizedBox(height: 10,),
                        SizedBox(
                          width: 300,
                          child: CustomTextFormField(
                            isExpanded: false,
                            label: "Telefono",
                            controller: phoneNumber,
                            isNumber: true,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Telefono è obbligatorio';
                              }
                              final phoneRegex = RegExp(r'^\+?\d+$');
                              if (!phoneRegex.hasMatch(value)) {
                                return 'Inserisci un numero di telefono valido';
                              }
                              return null;
                            },
                          ),
                        ),
                        SizedBox(height: 40,),
                        NarFormLabelWidget(
                          label: 'Immagine profilo',
                          fontSize: 16,
                          fontWeight: '700',
                        ),
                        SizedBox(height: 25,),
                        NarFilePickerWidget(
                          allowMultiple: false,
                          filesToDisplayInList: 0,
                          removeButton: true,
                          isDownloadable: false,
                          removeButtonText: 'Elimina',
                          removeButtonTextColor: AppColor.black,
                          uploadButtonPosition: 'back',
                          showMoreButtonText: '+ espandi',
                          actionButtonPosition: 'bottom',
                          displayFormat: 'inline-widget',
                          containerWidth: 86,
                          containerHeight: 86,
                          containerBorderRadius: 100,
                          borderRadius: 7,
                          fontSize: 11,
                          fontWeight: '600',
                          text: 'Carica Profile Pic',
                          borderSideColor: Theme.of(context).primaryColor,
                          hoverColor: Color.fromRGBO(133, 133, 133, 1),
                          allFiles: newPersone?.profilePicture,
                          pageContext: context,
                          storageDirectory: "agencyPersona/profile/${persone!.firebaseId}",
                          removeExistingOnChange: true,
                          progressMessage: [''],
                          notAccent: true,
                          showTitle: false,
                          onUploadCompleted: () {
                            if (mounted) {
                              _setState(() {});
                            }
                          },
                        ),
                        SizedBox(height: 25,),

                        //----Button
                        NarFilePickerWidget(
                          allowMultiple: false,
                          filesToDisplayInList: 0,
                          removeButton: true,
                          isDownloadable: false,
                          removeButtonText: 'Elimina',
                          removeButtonTextColor: AppColor.black,
                          uploadButtonPosition: 'back',
                          showMoreButtonText: '+ espandi',
                          actionButtonPosition: 'bottom',
                          displayFormat: 'inline-button',
                          containerWidth: 65,
                          containerHeight: 65,
                          containerBorderRadius: 8,
                          borderRadius: 7,
                          fontSize: 11,
                          fontWeight: '600',
                          text: 'Carica',
                          borderSideColor: Theme.of(context).primaryColor,
                          hoverColor: Color(0xFFE0EBF9),
                          allFiles: newPersone?.profilePicture,
                          pageContext: context,
                          storageDirectory: "agencyPersona/profile/${persone.firebaseId}",
                          removeExistingOnChange: true,
                          progressMessage: [''],
                          notAccent: true,
                          splashColor: Color(0xFFE0EBF9),
                          height: 35,
                          buttonWidth: 125,
                          buttonTextColor: Theme.of(context).primaryColor,
                          onUploadCompleted: () {
                            if (mounted) {
                              _setState(() {});
                            }
                          },
                        ),
                      ],
                    ),
                  )),
            );
          });
        });
  }


  void showSelectFontDialog({required BuildContext context,ReportAcquirente? reportAcquirente}) async {
   String fontType = reportAcquirente?.fontType ?? "";
    List<String> formErrorMessage = [];
    return showDialog(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context,_setState){
            return Center(
              child: BaseNewarcPopup(
                  buttonColor: Theme.of(context).primaryColor,
                  title: "Seleziona stile font",
                  buttonText: "Seleziona",
                  formErrorMessage: formErrorMessage,
                  onPressed: () async {
                    _setState((){
                      formErrorMessage.clear();
                      formErrorMessage.add("Salvataggio in corso...");
                    });
                    try{
                      setState(() {
                        reportAcquirente?.fontType = fontType;
                        isShowRedErrorBorderFont = false;
                      });
                      await FirebaseFirestore.instance
                          .collection(appConfig.COLLECT_REPORT_ACQUIRENTE)
                          .doc(reportAcquirente!.id)
                          .set(reportAcquirente.toMap());
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Salvato");
                      });
                    }catch(e,s){
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Si è verificato un errore.");
                      });
                      log("---------- ERROR While Selecting fonts ------> ${e.toString()}");
                    }
                  },
                  column: Container(
                    width: 400,
                    padding: EdgeInsets.symmetric(vertical: 25),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Expanded(
                          child: MouseRegion(
                            cursor: SystemMouseCursors.click,
                            child: GestureDetector(
                              child: Container(
                                height: 115,
                                padding: const EdgeInsets.symmetric(horizontal: 2,vertical: 5),
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: fontType == "raleway" ? const Color(0xff123A6F) : Color(0xFFD5D5D5),
                                    width: fontType == "raleway" ? 3 : 1,
                                  ),
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      children: [
                                        Icon(
                                          fontType == "raleway" ?  Icons.check_circle_rounded : Icons.circle_outlined,
                                          color: fontType == "raleway"  ? const Color(0xff123A6F) : const Color(0xFFD5D5D5),
                                          size: 25,
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: 20,),

                                    NarFormLabelWidget(
                                      label: "Minimal",
                                      fontWeight: '600',
                                      fontSize: 14,
                                    )
                                  ],
                                ),
                              ),
                              onTap: (){
                                _setState((){
                                  fontType = "raleway";
                                });
                              },
                            ),
                          ),
                        ),
                        SizedBox(width: 27),
                        Expanded(
                          child: MouseRegion(
                            cursor: SystemMouseCursors.click,
                            child: GestureDetector(
                              child: Container(
                                height: 115,
                                padding: const EdgeInsets.symmetric(horizontal: 2,vertical: 5),
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: fontType == "tinos" ? const Color(0xff123A6F) : Color(0xFFD5D5D5),
                                    width: fontType == "tinos" ? 3 : 1,
                                  ),
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      children: [
                                        Icon(
                                          fontType == "tinos" ?  Icons.check_circle_rounded : Icons.circle_outlined,
                                          color: fontType == "tinos"  ? const Color(0xff123A6F) : const Color(0xFFD5D5D5),
                                          size: 25,
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: 20,),

                                    Text("Elegant", style: TextStyle(
                                        fontFamily: "Tinos-700",
                                        fontSize: 16,
                                        color: AppColor.black
                                    ),
                                    )
                                  ],
                                ),
                              ),
                              onTap: (){
                                _setState((){
                                  fontType = "tinos";
                                });
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                  )),
            );
          });
        });
  }

  void showSelectTemplatePhotoDialog({required BuildContext context,ReportAcquirente? reportAcquirente}) async {
    List<String> formErrorMessage = [];
    String templateType = reportAcquirente?.photoTemplateType ?? "";
    return showDialog(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context,_setState){
            return Center(
              child: BaseNewarcPopup(
                  buttonColor: Theme.of(context).primaryColor,
                  title: "Template fotografie",
                  buttonText: "Seleziona",
                  formErrorMessage: formErrorMessage,
                  onPressed: () async {
                    _setState((){
                      formErrorMessage.clear();
                      formErrorMessage.add("Salvataggio in corso...");
                    });
                    try{
                      setState(() {
                        reportAcquirente?.photoTemplateType = templateType;
                        isShowRedErrorBorderPhotoTemplateType = false;
                      });
                      await FirebaseFirestore.instance
                          .collection(appConfig.COLLECT_REPORT_ACQUIRENTE)
                          .doc(reportAcquirente!.id)
                          .set(reportAcquirente.toMap());
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Salvato");
                      });
                    }catch(e,s){
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Si è verificato un errore.");
                      });
                      log("---------- ERROR While Agency Persone ------> ${e.toString()}");
                    }
                  },
                  column: Container(
                    width: 400,
                    padding: EdgeInsets.symmetric(vertical: 25),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        _templateOption(
                            type: "wide",
                            label: "Wide",
                          description: [
                            " • Immagini grandi",
                            " • 1 immagine/pagina"
                          ],
                          isSelected: templateType == "wide",
                          onTap: (){
                              _setState((){
                                templateType = "wide";
                              });
                          },
                          imgPath: "assets/icons/photo_template_wide.png"
                        ),
                        SizedBox(width: 20,),
                        _templateOption(
                            type: "short",
                            label: "Short",
                            description: [
                              " • Immagini piccole",
                              " • Tutte in una pagina"
                            ],
                            isSelected: templateType == "short",
                            onTap: (){
                              _setState((){
                                templateType = "short";
                              });
                            },
                          imgPath: "assets/icons/photo_template_short.png"
                        )
                      ],
                    ),
                  )),
            );
          });
        });
  }

  void showSelectTemplateCoverDialog({required BuildContext context,ReportAcquirente? reportAcquirente}) async {
    List<String> formErrorMessage = [];
    String templateType = reportAcquirente?.coverTemplateType ?? "";
    return showDialog(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context,_setState){
            return Center(
              child: BaseNewarcPopup(
                  buttonColor: Theme.of(context).primaryColor,
                  title: "Template copertina",
                  buttonText: "Seleziona",
                  formErrorMessage: formErrorMessage,
                  onPressed: () async {
                    _setState((){
                      formErrorMessage.clear();
                      formErrorMessage.add("Salvataggio in corso...");
                    });
                    try{
                      setState(() {
                        reportAcquirente?.coverTemplateType = templateType;
                        isShowRedErrorBorderCoverTemplateType = false;
                      });
                      await FirebaseFirestore.instance
                          .collection(appConfig.COLLECT_REPORT_ACQUIRENTE)
                          .doc(reportAcquirente!.id)
                          .set(reportAcquirente.toMap());
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Salvato");
                      });
                    }catch(e,s){
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Si è verificato un errore.");
                      });
                      log("---------- ERROR Cover Template ------> ${e.toString()}");
                    }
                  },
                  column: Container(
                    width: 400,
                    padding: EdgeInsets.symmetric(vertical: 25),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        _templateOption(
                            type: "classic",
                            label: "Classic",
                          description: [
                            " • Immagine metà pagina",
                            " • Titoli a sinistra"
                          ],
                          isSelected: templateType == "classic",
                          onTap: (){
                              _setState((){
                                templateType = "classic";
                              });
                          },
                            imgPath: "assets/icons/cover_classic.png"
                        ),
                        SizedBox(width: 20,),
                        _templateOption(
                            type: "full",
                            label: "Full",
                            description: [
                              " • Immagine piena",
                              " • Titoli centrati"
                            ],
                            isSelected: templateType == "full",
                            onTap: (){
                              _setState((){
                                templateType = "full";
                              });
                            },
                            imgPath: "assets/icons/cover_full.png"
                        )
                      ],
                    ),
                  )),
            );
          });
        });
  }

  Widget _templateOption({
    required String label,
    required String type,
    required List<String> description,
    required bool isSelected,
    required VoidCallback onTap,
    required String imgPath,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 2,vertical: 5),
          decoration: BoxDecoration(
            border: Border.all(
              color: isSelected ? const Color(0xff123A6F) : Color(0xFFD5D5D5),
              width: isSelected ? 3 : 1,
            ),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Row(
                children: [
                  Expanded(child: SizedBox()),
                  Expanded(
                    flex: 2,
                    child: Center(
                      child: NarFormLabelWidget(
                        label: label,
                        fontWeight: '700',
                        fontSize: 18,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: Icon(
                        isSelected ? Icons.check_circle_rounded : Icons.circle_outlined,
                        color: isSelected ? const Color(0xff123A6F) : const Color(0xFFD5D5D5),
                        size: 25,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: imgPath.isNotEmpty ? 9 : 0),
              imgPath.isNotEmpty ?
              Image.asset(imgPath) : SizedBox(),
              SizedBox(height: description.isNotEmpty ? 14 : 0),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ...description.map((desc) => NarFormLabelWidget(
                    label: desc,
                    fontWeight: '500',
                    fontSize: 12,
                  )),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }

}




class ReportItem {
  bool isCompleted;
  bool isIncluded;
  String section;
  ReportItem({required this.isCompleted, required this.isIncluded,required this.section});
}
