import 'dart:developer';

import 'package:flutter/material.dart';

class NewarcProjectFixedAssetsPropertyPagamento{
  String? newarcProjectFixedAssetsPropertyCategoryId;
  List<Rate>? rate;
  double? total;
  double? totalIVA;
  bool? hasConcessions;
  bool? isManualCategory;



  //--for UI
  String? categoryName;


  Map<String, Object?> toMap() {
    final map = {
      'newarcProjectFixedAssetsPropertyCategoryId': newarcProjectFixedAssetsPropertyCategoryId,
      'rate': rate?.map((val) => val.toMap()).toList(),
      'total': total,
      'totalIVA': totalIVA,
      'hasConcessions': hasConcessions,
      'isManualCategory': isManualCategory,
    };

    if (isManualCategory ?? false) {
      map['categoryName'] = categoryName;
    }else{
      categoryName = categoryName;
    }

    return map;
  }

  NewarcProjectFixedAssetsPropertyPagamento.empty() {
    this.newarcProjectFixedAssetsPropertyCategoryId = '';
    this.rate = [];
    this.total = 0.0;
    this.totalIVA = 0.0;
    this.categoryName = '';
    this.hasConcessions = false;
    this.isManualCategory = false;
  }

  NewarcProjectFixedAssetsPropertyPagamento.fromDocument(Map<String, dynamic> data, String id) {
    try {
      this.newarcProjectFixedAssetsPropertyCategoryId = data['newarcProjectFixedAssetsPropertyCategoryId'];
      this.total = data['total'];
      this.totalIVA = data['totalIVA'];
      this.hasConcessions = data['hasConcessions'] ?? false;
      this.isManualCategory = data['isManualCategory'] ?? false;
      this.rate = [];
      if (data['rate'] != null) {
        for (var i = 0; i < data['rate'].length; i++) {
          this.rate?.add(Rate.fromDocument(data['rate'][i],''));
        }
      }
      if (isManualCategory ?? false) {
        categoryName = data['categoryName'];
      } else {
        categoryName = categoryName;
      }

    } catch (e, s) {
      print({ 'NewarcProjectFixedAssetsPropertyPagamento Class Error ------->', e, s});
    }
  }
}


class Rate{
  String? uniqueId;
  String? newarcProjectFixedAssetsPercentageId;
  int? index;
  double? rate;
  double? paidAmount;
  int? ivaPercentage;
  double? ivaAmount;
  Future<List>? fetchPercentageFuture;
  bool? isPaid;
  int? paidDate;
  String? description;
  TextEditingController? descriptionController;

  List? margeRateUniqueIDS;
  String? mergedIntoRateId;
  bool? isMerged;
  List<EntratePayment>? entratePayment;
  String? paymentStatus;

  int? holdedPercentage; //--- Percentuale ritenuta
  double? holdedAmount; //--- Percentuale ritenuta
  double? expectedAmount; //--- Accredito atteso
  double? toBePaidAmount; //--- Da pagara


  // for UI
  int? percentage;
  String? category;






  Map<String, Object?> toMap() {
    percentage = percentage;
    return {
      'newarcProjectFixedAssetsPercentageId': newarcProjectFixedAssetsPercentageId,
      'uniqueId': uniqueId,
      'index': index,
      'rate': rate,
      'ivaPercentage': ivaPercentage,
      'ivaAmount': ivaAmount,
      'isPaid': isPaid,
      'paidDate': paidDate,
      'paidAmount': paidAmount,
      'description': description,
      'margeRateUniqueIDS': margeRateUniqueIDS,
      'isMerged': isMerged,
      'mergedIntoRateId': mergedIntoRateId,
      'paymentStatus': paymentStatus,
      'entratePayment': entratePayment?.map((val) => val.toMap()).toList(),
      'holdedPercentage': holdedPercentage,
      'holdedAmount': holdedAmount,
      'expectedAmount': expectedAmount,
      'toBePaidAmount': toBePaidAmount,
    };
  }

  Rate.empty() {
    this.newarcProjectFixedAssetsPercentageId = '';
    this.index = 0;
    this.uniqueId = "";
    this.rate = 0.0;
    this.ivaPercentage = 0;
    this.ivaAmount = 0;
    this.isPaid = false;
    this.paidDate = null;
    this.percentage = 0;
    this.paidAmount = 0;
    this.category = "";
    this.description = "";
    this.descriptionController?.clear();
    this.margeRateUniqueIDS = [];
    this.entratePayment = [];
    this.isMerged = false;
    this.mergedIntoRateId = "";
    this.paymentStatus = "";
    this.paymentStatus = "in attesa";
    this.holdedPercentage = 0;
    this.holdedAmount = 0;
    this.expectedAmount = 0;
    this.toBePaidAmount = 0;
  }

  Rate.fromDocument(Map<String, dynamic> data, String id) {
    percentage = percentage;
    try {
      this.newarcProjectFixedAssetsPercentageId = data['newarcProjectFixedAssetsPercentageId'];
      this.uniqueId = data['uniqueId'];
      this.index = data['index'];
      this.rate = data['rate'];
      this.ivaPercentage = data['ivaPercentage'];
      this.isPaid = data['isPaid'];
      this.paidDate = data['paidDate'];
      this.ivaAmount = data['ivaAmount'];
      this.paidAmount = data['paidAmount'];
      this.description = data['description'];
      this.margeRateUniqueIDS = data['margeRateUniqueIDS'];
      this.isMerged = data['isMerged'] ?? false;
      this.mergedIntoRateId = data['mergedIntoRateId'];
      this.descriptionController = TextEditingController(text: this.description);
      this.paymentStatus = data['paymentStatus'];
      this.holdedPercentage = data['holdedPercentage'];
      this.holdedAmount = data['holdedAmount'];
      this.expectedAmount = data['expectedAmount'];
      this.toBePaidAmount = data['toBePaidAmount'];
      this.entratePayment = [];
      if (data['entratePayment'] != null) {
        for (var i = 0; i < data['entratePayment'].length; i++) {
          this.entratePayment?.add(EntratePayment.fromDocument(data['entratePayment'][i]));
        }
      }
    } catch (e, s) {
      print({ 'Rate Class Error ------->', e, s});
    }
  }
}


class EntratePayment{
  String? uniqueId;
  String? rateUniqueId;
  int? paidDate;
  Map? invoicePath;
  String? paymentStatus;
  String? paymentType;
  double? remainingAmount; //----- Rimanenza
  double? paidAmount; //--- Somma bonificata
  double? receivedAmount; //--- Somma ricevuta


  // for UI
  List? invoiceImages;
  bool? isInvoiceUploaded;



  Map<String, Object?> toMap() {
    invoiceImages = invoiceImages;
    isInvoiceUploaded = isInvoiceUploaded;
    return {
      'uniqueId': uniqueId,
      'paidDate': paidDate,
      'invoicePath': invoicePath,
      'paidAmount': paidAmount,
      'paymentStatus': paymentStatus,
      'rateUniqueId': rateUniqueId,
      'paymentType': paymentType,
      'remainingAmount': remainingAmount,
      'receivedAmount': receivedAmount,
    };
  }

  EntratePayment.empty() {
    this.uniqueId = "";
    this.rateUniqueId = "";
    this.paidDate = null;
    this.invoicePath = null;
    this.paidAmount = 0;
    this.invoiceImages = [];
    isInvoiceUploaded = false;
    this.paymentStatus = "in attesa";
    this.paymentType = "";
    this.remainingAmount = 0;
    this.receivedAmount = 0;
  }

  EntratePayment.fromDocument(Map<String, dynamic> data) {
    try {
      this.rateUniqueId = data['rateUniqueId'];
      this.uniqueId = data['uniqueId'];
      this.paidDate = data['paidDate'];
      this.invoicePath = data['invoicePath'];
      this.paidAmount = data['paidAmount'] ?? 0;
      this.paymentStatus = data['paymentStatus'];
      this.paymentType = data['paymentType'];
      this.remainingAmount = data['remainingAmount'] ?? 0;
      this.receivedAmount = data['receivedAmount'] ?? 0;
      invoiceImages = (data['invoicePath'] != null && (data['invoicePath'] as Map).isNotEmpty) ? [data['invoicePath']["filename"]] : [];
      isInvoiceUploaded = (data['invoicePath'] != null && (data['invoicePath'] as Map).isNotEmpty) ? true : false;
    } catch (e, s) {
      print({ 'Payment Class Error ------->', e, s});
    }
  }
}


