import 'package:flutter/material.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/tab/text_style.dart';

class TagWidget extends StatelessWidget {
  const TagWidget({
    super.key,
    this.text,
    this.textStyle,
    this.textColor,
    this.borderRadius,
    this.statusColor,
  });

  final String? text;
  final TextStyle? textStyle;
  final Color? textColor, statusColor;
  final double? borderRadius;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        left: 6,
        right: 6,
        bottom: 3,
        top: 0,
      ),
      decoration: BoxDecoration(
        color: statusColor ?? AppColor.successGreenColor,
        borderRadius: BorderRadius.circular(
          borderRadius ?? 100,
        ),
      ),
      // child: Text(
      //   text ?? "",
      //   style: textStyle ?? TextStyle(height: 1).text10w700.textColor(textColor ?? AppColor.white).letterSpace(0.5),
      // ),
      child: NarFormLabelWidget(
        label: text ?? "",
        fontSize: 10,
        fontWeight: '500',
        textColor: textColor ?? AppColor.white,
        letterSpacing: 0.5,
      ),
    );
  }
}
