import 'package:newarc_platform/classes/immaginaProject.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;

const gSFUpperLimit = 300;
const professionalsGSFUpperLimit = 280;

class ImmaginaProjectEconomics {
  final ImmaginaProject project;
  static const double VATPerc = .22;
  static Map<String, dynamic> noWatermarkCost = {'price': 150, 'stripeId': appConfig.isProduction? "price_1Qtr6oBCCTTeQsGGB1F1fPLf" : 'price_1QRt3lBCCTTeQsGGtNv3V3f2'};
  static Map<String, Map<String, dynamic>> renderRequestFee = {
    'small': {'price': 399, 'stripeId': appConfig.isProduction? "price_1QtrDaBCCTTeQsGG7Rn7d7me" : 'price_1Qut1BBCCTTeQsGGiT5ckrO1'},
    'medium': {'price': 449, 'stripeId': appConfig.isProduction? "price_1ROExKBCCTTeQsGGf9twRFzt" : 'price_1Qut1kBCCTTeQsGGNBkhGOwq'},
    'large': {'price': 499, 'stripeId': appConfig.isProduction? "price_1ROEwiBCCTTeQsGGnirFWY4h" : 'price_1Qut3IBCCTTeQsGGX8wCk4CR'},
    'xlarge': {'price': 549, 'stripeId': appConfig.isProduction? "price_1ROEw7BCCTTeQsGGbe405Q9L" : 'price_1Qut3zBCCTTeQsGGdBnd0LXZ'},
    'xxlarge': {'price': 599, 'stripeId': appConfig.isProduction? "price_1QucNGBCCTTeQsGG3TqCAezj" : 'price_1Qut4fBCCTTeQsGGQ7pI5ZOu'},
    'xxxlarge': {'price': 649, 'stripeId': appConfig.isProduction? "price_1QucO9BCCTTeQsGGOaxSy1Nz" : 'price_1Qut5NBCCTTeQsGGGnpnG6PF'},
  };
  static Map<String, Map<String, dynamic>> newarcMediaFee = {
    'small': {'price': 79, 'stripeId': appConfig.isProduction? "price_1QtrDhBCCTTeQsGGeSDYYxWo" : 'price_1Qut6PBCCTTeQsGGDobGb3Fe'},
    'medium': {'price': 89, 'stripeId': appConfig.isProduction? "price_1QtrDjBCCTTeQsGGdrNHF9Dv" : 'price_1Qut7DBCCTTeQsGG8coWFDwT'},
    'large': {'price': 99, 'stripeId': appConfig.isProduction? "price_1QtrDlBCCTTeQsGGXOout35e" : 'price_1Qut7kBCCTTeQsGG600MfHCe'},
    'xlarge': {'price': 109, 'stripeId': appConfig.isProduction? "price_1QucD8BCCTTeQsGGGstz2Fkm" : 'price_1Qut8OBCCTTeQsGGoxk5wIWA'},
    'xxlarge': {'price': 119, 'stripeId': appConfig.isProduction? "price_1QucUpBCCTTeQsGGrOq6uLLJ" : 'price_1Qut9QBCCTTeQsGGZKo5r49E'},
    'xxxlarge': {'price': 129, 'stripeId': appConfig.isProduction? "price_1QucVTBCCTTeQsGGWGRQ7aXh" : 'price_1QutEkBCCTTeQsGGpbV7HyB8'},
  };
  static Map<String, Map<String, dynamic>> newarcRilieviFee = {
    'small': {'price': 149, 'stripeId': appConfig.isProduction? "price_1QucFvBCCTTeQsGGecJiXHjm" : 'price_1QutGOBCCTTeQsGGLIhR6nv0'},
    'medium': {'price': 199, 'stripeId': appConfig.isProduction? "price_1QucFxBCCTTeQsGGNfsFVSBU" : 'price_1QutHFBCCTTeQsGGCgfTrAil'},
    'large': {'price': 249, 'stripeId': appConfig.isProduction? "price_1QucFzBCCTTeQsGGfqQdDNUJ" : 'price_1QutHoBCCTTeQsGGXtJmQQb2'},
    'xlarge': {'price': 299, 'stripeId': appConfig.isProduction? "price_1QucG0BCCTTeQsGGs3IGIacO" : 'price_1QutJ4BCCTTeQsGGQpOVPxoB'},
    'xxlarge': {'price': 349, 'stripeId': appConfig.isProduction? "price_1QucYaBCCTTeQsGG9mvqHuTX" : 'price_1QutJvBCCTTeQsGG4W214aPv'},
    'xxxlarge': {'price': 399, 'stripeId': appConfig.isProduction? "price_1QucZcBCCTTeQsGGVfkws0ka" : 'price_1QutKnBCCTTeQsGG6LHsH1vb'},
  };
  static Map<String, Map<String, dynamic>> professionalsRenderRequestFee = {
    'mini': {'price': 1099, 'stripeId': appConfig.isProduction? "price_1RZUboBCCTTeQsGGIPaWILDt" : 'price_1RVpjMBCCTTeQsGGQU2iZA3I'},
    'small': {'price': 1199, 'stripeId': appConfig.isProduction? "price_1RZUbrBCCTTeQsGGEXVQfG1n" : 'price_1RVpl4BCCTTeQsGG9IpjqHdb'},
    'medium': {'price': 1299, 'stripeId': appConfig.isProduction? "price_1RZUbtBCCTTeQsGGygxfNzAe" : 'price_1RVpn7BCCTTeQsGGvzxX2zyn'},
    'large': {'price': 1399, 'stripeId': appConfig.isProduction? "price_1RZUbvBCCTTeQsGGNzjU0ep8" : 'price_1RVpnyBCCTTeQsGG6w65uCCY'},
    'xlarge': {'price': 1499, 'stripeId': appConfig.isProduction? "price_1RZUbxBCCTTeQsGGFUxcW5Ui" : 'price_1RVppDBCCTTeQsGG90znrKcV'},
    'xxlarge': {'price': 1599, 'stripeId': appConfig.isProduction? "price_1RZUc0BCCTTeQsGGStgurXFt" : 'price_1RVpqJBCCTTeQsGGdnyJTtFC'},
    'xxxlarge': {'price': 1699, 'stripeId': appConfig.isProduction? "price_1RZUc3BCCTTeQsGG5lbedklI" : 'price_1RVprSBCCTTeQsGG2hlqshf1'},
  };  
  static Map<String, Map<String, dynamic>> professionalsOptionals = {
    // 'Revisione progetto': {
    //   'price': 299, 
    //   'description': 'Es: modifica muri, porte, infissi, arredi.',
    //   'stripeId': appConfig.isProduction? "price_1RZUZ0BCCTTeQsGGyTYXFB0t" : 'price_1RUTa3BCCTTeQsGGSgkbSGis'},
    // 'Aggiunta/modifica materiali': {
    //   'price': 199, 
    //   'description': 'Es: sostituzione/modifica di pavimenti, rivestimenti e decorativi parete.',
    //   'stripeId': appConfig.isProduction? "price_1RZUZ3BCCTTeQsGG4LIfbdYp" : 'price_1RUTbbBCCTTeQsGGvzumktCQ'},
    'Video interni': {
      'price': 299, 
      'description': 'Realizzazione di video render della durata compresa tra 30 secondi e 1 minuto.',
      'stripeId': appConfig.isProduction? "price_1RZUZ7BCCTTeQsGGejoJ1Gu7" : 'price_1RUTdMBCCTTeQsGG7HxcYzNy'},
    'Video esterni': {
      'price': 499, 
      'description': 'Realizzazione di video render della durata compresa tra 30 secondi e 1 minuto.',
      'stripeId': appConfig.isProduction? "price_1RZUZ9BCCTTeQsGG1pq7CJhh" : 'price_1RUTeBBCCTTeQsGGGDCfNTxf'},
    'Render vista esterna aggiuntiva': {
      'price': 399, 
      'description': 'Realizzazione di una vista esterna aggiuntiva dello stabile o della costruzione.',
      'stripeId': appConfig.isProduction? "price_1RZUZDBCCTTeQsGGFYUsm8BS" : 'price_1RUVa4BCCTTeQsGGhVa7ZWeq'},
    'Planimetria aggiuntiva': {
      'price': 129, 
      'description': 'Realizzazione di una planimetria aggiuntiva con differente distribuzione degli spazi/arredi.',
      'stripeId': appConfig.isProduction? "price_1RZUZHBCCTTeQsGG0sJkhZRS" : 'price_1RUThNBCCTTeQsGGmE88Az4Z'},
  };

  static List<Map<String, dynamic>> generatePricingData() {
    return [
      {
        "title": "Request Fee",
        "prices": [
          {"label": "Immobili fino a 49mq", "price": "${renderRequestFee['small']?['price']}€ + iva"},
          {"label": "Immobili da 50mq a 99mq", "price": "${renderRequestFee['medium']?['price']}€ + iva"},
          {"label": "Immobili da 100mq a 149mq", "price": "${renderRequestFee['large']?['price']}€ + iva"},
        ],
        "extraPrices": [
          {"label": "Immobili da 150mq a 199mq", "price": "${renderRequestFee['xlarge']?['price']}€ + iva"},
          {"label": "Immobili da 200mq a 249mq", "price": "${renderRequestFee['xxlarge']?['price']}€ + iva"},
          {"label": "Immobili da 250mq a 299mq", "price": "${renderRequestFee['xxxlarge']?['price']}€ + iva"},
        ],
      },
      {
        "title": "Newarc Media",
        "prices": [
          {"label": "Immobili fino a 49mq", "price": "${newarcMediaFee['small']?['price']}€ + iva"},
          {"label": "Immobili da 50mq a 99mq", "price": "${newarcMediaFee['medium']?['price']}€ + iva"},
          {"label": "Immobili da 100mq a 149mq", "price": "${newarcMediaFee['large']?['price']}€ + iva"},
        ],
        "extraPrices": [
          {"label": "Immobili da 150mq a 199mq", "price": "${newarcMediaFee['xlarge']?['price']}€ + iva"},
          {"label": "Immobili da 200mq a 249mq", "price": "${newarcMediaFee['xxlarge']?['price']}€ + iva"},
          {"label": "Immobili da 250mq a 299mq", "price": "${newarcMediaFee['xxxlarge']?['price']}€ + iva"},
        ],
      },
      {
        "title": "Newarc Rilievi",
        "prices": [
          {"label": "Immobili fino a 49mq", "price": "${newarcRilieviFee['small']?['price']}€ + iva"},
          {"label": "Immobili da 50mq a 99mq", "price": "${newarcRilieviFee['medium']?['price']}€ + iva"},
          {"label": "Immobili da 100mq a 149mq", "price": "${newarcRilieviFee['large']?['price']}€ + iva"},
        ],
        "extraPrices": [
          {"label": "Immobili da 150mq a 199mq", "price": "${newarcRilieviFee['xlarge']?['price']}€ + iva"},
          {"label": "Immobili da 200mq a 249mq", "price": "${newarcRilieviFee['xxlarge']?['price']}€ + iva"},
          {"label": "Immobili da 250mq a 299mq", "price": "${newarcRilieviFee['xxxlarge']?['price']}€ + iva"},
        ],
      },
    ];
  }

  static List<Map<String, dynamic>> generateProfessionalsPricingData() {
    return [
      {
        "title": "Unità immobiliari",
        "prices": [
          {"label": "Unità fino a 39mq", "price": "${professionalsRenderRequestFee['mini']?['price']}€ + iva"},
          {"label": "Unità da 40mq a 79mq", "price": "${professionalsRenderRequestFee['small']?['price']}€ + iva"},
          {"label": "Unità da 80mq a 119mq", "price": "${professionalsRenderRequestFee['medium']?['price']}€ + iva"},
        ],
        "extraPrices": [
          {"label": "Unità da 120mq a 159mq", "price": "${professionalsRenderRequestFee['large']?['price']}€ + iva"},
          {"label": "Unità da 160mq a 199mq", "price": "${professionalsRenderRequestFee['xlarge']?['price']}€ + iva"},
          {"label": "Unità da 200mq a 239mq", "price": "${professionalsRenderRequestFee['xxlarge']?['price']}€ + iva"},
          {"label": "Unità da 240mq a 279mq", "price": "${professionalsRenderRequestFee['xxxlarge']?['price']}€ + iva"},
        ],
      },
      {
        "title": "Optionals",
        "prices": [
          {"label": "Revisione progetto", "price": "${professionalsOptionals['Revisione progetto']?['price'] ?? 299}€ + iva"},
          {"label": "Video interni", "price": "${professionalsOptionals['Video interni']?['price']}€ + iva"},
          {"label": "Video esterni", "price": "${professionalsOptionals['Video esterni']?['price']}€ + iva"},
        ],
        "extraPrices": [
          {"label": "Aggiunta/modifica materiali", "price": "${professionalsOptionals['Aggiunta/modifica materiali']?['price'] ?? 199}€ + iva"},
          {"label": "Planimetria aggiuntiva", "price": "${professionalsOptionals['Planimetria aggiuntiva']?['price']}€ + iva"},
          {"label": "Render vista esterna aggiuntiva", "price": "${professionalsOptionals['Render vista esterna aggiuntiva']?['price']}€ + iva"},
        ],
      },
    ];
  }

  ImmaginaProjectEconomics({required this.project}) {
    if (project.grossSquareFootage == null && !project.isForProfessionals) {
      throw ArgumentError('The "grossSquareFootage" attribute in ImmaginaProject must not be null.');
    } else if (project.isForProfessionals && (project.childrenProjects == null || project.childrenProjects!.isEmpty)) {
      throw ArgumentError('The "childrenProjects" attribute in ImmaginaProject for Professionals must not be null.');
    }
  }

  List<String> getStripePriceIds(bool isSubscriptionOver) {
    List<String> priceIds = [];
    String? size;
    if (this.project.grossSquareFootage! < 50) {
      size = 'small';
    } else if (this.project.grossSquareFootage! < 100) {
      size = 'medium';
    } else if (this.project.grossSquareFootage! < 150) {
      size = 'large';
    } else if (this.project.grossSquareFootage! < 200) {
      size = 'xlarge';
    } else if (this.project.grossSquareFootage! < 250) {
      size = 'xxlarge';
    } else if (this.project.grossSquareFootage! < gSFUpperLimit) {
      size = 'xxxlarge';
    }

    if (isSubscriptionOver) {
      priceIds.add(renderRequestFee[size]!['stripeId']);
    }

    if (this.project.wantsNewarcPictures ?? false) {
      priceIds.add(newarcMediaFee[size]!['stripeId']);
    }
    if (this.project.wantsNewarcPlanimetry ?? false) {
      priceIds.add(newarcRilieviFee[size]!['stripeId']);
    }
    if (!this.project.wantsWatermark) {
      priceIds.add(noWatermarkCost['stripeId']);
    }
    return priceIds;
  }

  List<String> getStripePriceIdsForProfessional() {
    List<String> priceIds = [];
    for (var taglio in this.project.childrenProjects!) {
      String? size;
      if (taglio.grossSquareFootage! < 40) {
        size = 'mini';
      } else if (taglio.grossSquareFootage! < 80) {
        size = 'small';
      } else if (taglio.grossSquareFootage! < 120) {
        size = 'medium';
      } else if (taglio.grossSquareFootage! < 160) {
        size = 'large';
      } else if (taglio.grossSquareFootage! < 200) {
        size = 'xlarge';
      } else if (taglio.grossSquareFootage! < 240) {
        size = 'xxlarge';
      } else if (taglio.grossSquareFootage! < professionalsGSFUpperLimit) {
        size = 'xxxlarge';
      }
      priceIds.add(professionalsRenderRequestFee[size]!['stripeId']);
    }
    for (var optional in this.project.optionals) {
      priceIds.add(professionalsOptionals[optional]!['stripeId']);
    }
    return priceIds;
  }


  int? computeRenderRequestFee() {
    var tot;
    if (this.project.grossSquareFootage! < 50) {
      tot = renderRequestFee['small']!['price'];
    } else if (this.project.grossSquareFootage! < 100) {
      tot = renderRequestFee['medium']!['price'];
    } else if (this.project.grossSquareFootage! < 150) {
      tot = renderRequestFee['large']!['price'];
    } else if (this.project.grossSquareFootage! < 200) {
      tot = renderRequestFee['xlarge']!['price'];
    } else if (this.project.grossSquareFootage! < 250) {
      tot = renderRequestFee['xxlarge']!['price'];
    } else if (this.project.grossSquareFootage! < gSFUpperLimit) {
      tot = renderRequestFee['xxxlarge']!['price'];
    }

    if (!this.project.wantsWatermark) {
      tot += noWatermarkCost['price'];
    }
    return tot;
  }

  int? computeProfessionalsRenderRequestFee(int childIndex) {
    var tot;
    if (this.project.childrenProjects![childIndex].grossSquareFootage! < 40) {
      tot = professionalsRenderRequestFee['mini']!['price'];
    } else if (this.project.childrenProjects![childIndex].grossSquareFootage! < 80) {
      tot = professionalsRenderRequestFee['small']!['price'];
    } else if (this.project.childrenProjects![childIndex].grossSquareFootage! < 120) {
      tot = professionalsRenderRequestFee['medium']!['price'];
    } else if (this.project.childrenProjects![childIndex].grossSquareFootage! < 160) {
      tot = professionalsRenderRequestFee['large']!['price'];
    } else if (this.project.childrenProjects![childIndex].grossSquareFootage! < 200) {
      tot = professionalsRenderRequestFee['xlarge']!['price'];
    } else if (this.project.childrenProjects![childIndex].grossSquareFootage! < 240) {
      tot = professionalsRenderRequestFee['xxlarge']!['price'];
    } else if (this.project.childrenProjects![childIndex].grossSquareFootage! < professionalsGSFUpperLimit) {
      tot = professionalsRenderRequestFee['xxxlarge']!['price'];
    }

    return tot;
  }

  int? computeNewarcMediaFee() {
    int tot = 0;
    if (this.project.wantsNewarcPictures ?? false) {
      if (this.project.grossSquareFootage! < 50) {
        tot = newarcMediaFee['small']!['price'];
      } else if (this.project.grossSquareFootage! < 100) {
        tot = newarcMediaFee['medium']!['price'];
      } else if (this.project.grossSquareFootage! < 150) {
        tot = newarcMediaFee['large']!['price'];
      } else if (this.project.grossSquareFootage! < 200) {
        tot = newarcMediaFee['xlarge']!['price'];
      } else if (this.project.grossSquareFootage! < 250) {
        tot = newarcMediaFee['xxlarge']!['price'];
      } else if (this.project.grossSquareFootage! < gSFUpperLimit) {
        tot = newarcMediaFee['xxxlarge']!['price'];
      }
    }
    return tot;
  }

  int? computeNewarcRilieviFee() {
    int tot = 0;
    if (this.project.wantsNewarcPlanimetry ?? false) {
      if (this.project.grossSquareFootage! < 50) {
        tot = newarcRilieviFee['small']!['price'];
      } else if (this.project.grossSquareFootage! < 100) {
        tot = newarcRilieviFee['medium']!['price'];
      } else if (this.project.grossSquareFootage! < 150) {
        tot = newarcRilieviFee['large']!['price'];
      } else if (this.project.grossSquareFootage! < 200) {
        tot = newarcRilieviFee['xlarge']!['price'];
      } else if (this.project.grossSquareFootage! < 250) {
        tot = newarcRilieviFee['xxlarge']!['price'];
      } else if (this.project.grossSquareFootage! < gSFUpperLimit) {
        tot = newarcRilieviFee['xxxlarge']!['price'];
      }
    }
    return tot;
  }

  double? computeTotalVAT() {
    if (this.project.grossSquareFootage! < gSFUpperLimit) {
      return (computeRenderRequestFee()! + computeNewarcMediaFee()! + computeNewarcRilieviFee()!) * (VATPerc);
    }
  }

  double? computeTotalCost() {
    if (this.project.grossSquareFootage! < gSFUpperLimit) {
      return (computeRenderRequestFee()! + computeNewarcMediaFee()! + computeNewarcRilieviFee()!) * (1 + VATPerc);
    }
  }

  double? computeNewarcMediaRilieviFeeTotalVAT() {
    if (this.project.grossSquareFootage! < gSFUpperLimit) {
      return (computeNewarcMediaFee()! + computeNewarcRilieviFee()!) * (VATPerc);
    }
  }

  double? computeNewarcMediaRilieviFeeTotalCost() {
    if (this.project.grossSquareFootage! < gSFUpperLimit) {
      return (computeNewarcMediaFee()! + computeNewarcRilieviFee()!) * (1 + VATPerc);
    }
  }

  int computeProfessionalsTotalCost() {
    return this.project.childrenProjects!.map((child) {
      int idx = this.project.childrenProjects!.indexOf(child);
      return(this.computeProfessionalsRenderRequestFee(idx) ?? 0);
    }).toList().reduce((prev, element) => prev + element);
  }

  Map computeProfessionalsDiscount() {
    double discountPerc = 0;
    double discountAmount = 0;
    Map discount = {};
    if (this.project.childrenProjects!.length > 1) {
      if (this.project.childrenProjects!.length < 6) {
        discountPerc = this.project.childrenProjects!.length/10;
      } else {
        discountPerc = 50/100;
      }
    }
    discount['discountPerc'] = discountPerc;
    discount['discountAmount'] = ((discountPerc*this.computeProfessionalsTotalCost())*100).round() / 100;
    return discount;
  }

  double computeProfessionalsTotalCostWithDiscount() {
    return ((this.computeProfessionalsTotalCost() - this.computeProfessionalsDiscount()['discountAmount'])*100).round() / 100;
  }

  double computeProfessionalsOptionalsCost() {
    if (this.project.optionals.isEmpty) {
      return 0;
    }
    return this.project.optionals.map((optional) {
      if (professionalsOptionals.containsKey(optional)) {
        return professionalsOptionals[optional]!['price'];
      } else {
        return 0;
      }
    }).toList().reduce((prev, element) => prev + element);
  }

  double computeProfessionalsTotalVAT() {
    return (((this.computeProfessionalsTotalCostWithDiscount() + this.computeProfessionalsOptionalsCost()) * VATPerc)*100).round() / 100;
  }
}
