import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/utils/color_schema.dart';

class CustomDrawer extends StatefulWidget {
  CustomDrawer(
      {Key? key, this.updateViewCallback, this.agencyUser, this.selectedView})
      : super(key: key);

  final Function? updateViewCallback;
  final AgencyUser? agencyUser;
  String? selectedView;

  @override
  State<CustomDrawer> createState() => _CustomDrawerState();
}

class _CustomDrawerState extends State<CustomDrawer> {
  bool immaginaExpanded = false;
  ExpansionTileController immaginaTile = new ExpansionTileController();
  bool contattiTileExpand = false;
  ExpansionTileController contattiTileKey = new ExpansionTileController();
  bool reportTileExpand = false;
  ExpansionTileController reportTileKey = new ExpansionTileController();
  bool strumentiTileExpand = false;
  ExpansionTileController strumentiTileKey = new ExpansionTileController();

  bool isDrawerOpen = true;

  void toggleDrawer() {
    setState(() {
      isDrawerOpen = !isDrawerOpen;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        AnimatedContainer(
          duration: Duration(milliseconds: 300),
          width: isDrawerOpen ? 257 : 80,
          color: Theme.of(context).primaryColorDark,
          child: Container(
            child: !isDrawerOpen
                ? Column(
                    children: [
                      Align(
                        alignment: Alignment.centerLeft,
                        child: Padding(
                          padding:
                              EdgeInsets.only(left: 20, right: 20, top: 42),
                          child: Image.asset(
                            "assets/home-agencies.png",
                            height: 25,
                          ),
                        ),
                      ),
                    ],
                  )
                : Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: ListView(
                          shrinkWrap: true,
                          padding: EdgeInsets.all(0),
                          children: [
                            Align(
                              alignment: Alignment.centerLeft,
                              child: Padding(
                                padding: EdgeInsets.only(
                                    left: 20, right: 20, top: 42),
                                child: Image.asset(
                                  'assets/logo-agenzie-white.png',
                                  height: 50,
                                ),
                              ),
                            ),
                            SizedBox(height: 40),
                            Theme(
                              data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
                              child: ExpansionTile(
                                key: Key('immagina'),
                                controller: immaginaTile,
                                initiallyExpanded: immaginaExpanded,
                                onExpansionChanged: ((value) => setState(
                                      () {
                                        immaginaExpanded = value;
                                      },
                                    )),
                                collapsedIconColor: Colors.white,
                                iconColor: Colors.white,
                                minTileHeight: 0,
                                childrenPadding: EdgeInsets.symmetric(horizontal: 15),
                                tilePadding: EdgeInsets.only(left: 15, right: 15, top: 5),
                                leading: SizedBox(
                                  height: 30,
                                  width: 30,
                                  child: Center(
                                    child: SvgPicture.asset(
                                      "assets/icons/ic_newarc.svg",
                                      height: 20,
                                      width: 21,
                                      alignment: Alignment.center,
                                    ),
                                  ),
                                ),
                                title: Text(
                                  'Newarc Immagina',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontFamily: 'semi-bold',
                                    color: Colors.white,
                                  ),
                                ),
                                trailing: SvgPicture.asset(
                                  immaginaExpanded ? 'assets/icons/arrow_up.svg' : 'assets/icons/arrow_down.svg',
                                  height: 6,
                                  width: 10,
                                  color: Colors.white,
                                ),
                                children: <Widget>[
                                  Padding(
                                    padding: const EdgeInsets.only(left: 42),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      children: [
                                        InkWell(
                                          onTap: () {
                                            widget.selectedView = 'progetti-attivi';
                                            widget.updateViewCallback!(widget.selectedView);
                                          },
                                          child: Row(
                                            children: [
                                              Text(
                                                'Progetti attivi',
                                                style: TextStyle(
                                                  fontSize: 14,
                                                  fontFamily: 'Raleway-400',
                                                  color: getColor('progetti-attivi'),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        SizedBox(
                                          height: 8,
                                        ),
                                        InkWell(
                                          onTap: () {
                                            widget.selectedView = 'progetti-archiviati';
                                            widget.updateViewCallback!(widget.selectedView);
                                          },
                                          child: Row(
                                            children: [
                                              Text(
                                                'Progetti archiviati',
                                                style: TextStyle(
                                                  fontSize: 14,
                                                  fontFamily: 'Raleway-400',
                                                  color: getColor('progetti-archiviati'),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Theme(
                              data: Theme.of(context)
                                  .copyWith(dividerColor: Colors.transparent),
                              child: ExpansionTile(
                                key: Key('strumenti'),
                                controller: strumentiTileKey,
                                initiallyExpanded: strumentiTileExpand,
                                onExpansionChanged: ((value) => setState(
                                      () {
                                    strumentiTileExpand = value;
                                  },
                                )),
                                collapsedIconColor: Colors.white,
                                iconColor: Colors.white,
                                leading: SizedBox(
                                  height: 30,
                                  width: 30,
                                  child: Center(
                                    child: SvgPicture.asset(
                                      "assets/icons/ic_report.svg",
                                      height: 20,
                                      width: 21,
                                      alignment: Alignment.center,
                                    ),
                                  ),
                                ),
                                minTileHeight: 0,
                                childrenPadding:
                                EdgeInsets.symmetric(horizontal: 15),
                                tilePadding: EdgeInsets.only(
                                    left: 15, right: 15, top: 5),
                                title: Text(
                                  'Newarc Reports',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontFamily: 'semi-bold',
                                    color: Colors.white,
                                  ),
                                ),
                                trailing: SvgPicture.asset(
                                  strumentiTileExpand
                                      ? 'assets/icons/arrow_up.svg'
                                      : 'assets/icons/arrow_down.svg',
                                  height: 6,
                                  width: 10,
                                  color: Colors.white,
                                ),
                                children: <Widget>[
                                  Padding(
                                    padding: const EdgeInsets.only(left: 42),
                                    child: Column(
                                      crossAxisAlignment:
                                      CrossAxisAlignment.start,
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      children: [
                                        InkWell(
                                          onTap: () {
                                            widget.selectedView = 'report-acquirente';
                                            widget.updateViewCallback!(widget.selectedView);
                                          },
                                          child: Row(
                                            children: [
                                              Text(
                                                'Report Acquirente',
                                                style: TextStyle(
                                                  fontSize: 14,
                                                  fontFamily: 'Raleway-400',
                                                  color: getColor('report-acquirente'),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Theme(
                              data: Theme.of(context)
                                  .copyWith(dividerColor: Colors.transparent),
                              child: ExpansionTile(
                                key: Key('reporting'),
                                controller: reportTileKey,
                                initiallyExpanded: reportTileExpand,
                                onExpansionChanged: ((value) => setState(
                                      () {
                                        reportTileExpand = value;
                                      },
                                    )),
                                collapsedIconColor: Colors.white,
                                iconColor: Colors.white,
                                minTileHeight: 0,
                                childrenPadding:
                                    EdgeInsets.symmetric(horizontal: 15),
                                tilePadding: EdgeInsets.only(
                                    left: 15, right: 15, top: 5),
                                leading: SizedBox(
                                  height: 30,
                                  width: 30,
                                  child: Center(
                                    child: SvgPicture.asset(
                                      "assets/icons/ic_segnala.svg",
                                      height: 20,
                                      width: 21,
                                      alignment: Alignment.center,
                                    ),
                                  ),
                                ),
                                title: Text(
                                  'Segnala',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontFamily: 'semi-bold',
                                    color: Colors.white,
                                  ),
                                ),
                                trailing: SvgPicture.asset(
                                  reportTileExpand
                                      ? 'assets/icons/arrow_up.svg'
                                      : 'assets/icons/arrow_down.svg',
                                  height: 6,
                                  width: 10,
                                  color: Colors.white,
                                ),
                                children: <Widget>[
                                  Padding(
                                    padding: const EdgeInsets.only(left: 42),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      children: [
                                        InkWell(
                                          onTap: () {
                                            widget.selectedView = 'suggested-contacts';
                                            widget.updateViewCallback!(
                                                widget.selectedView);
                                          },
                                          child: Row(
                                            children: [
                                              Text(
                                                'Segnala ristrutturazione',
                                                style: TextStyle(
                                                  fontSize: 14,
                                                  fontFamily: 'Raleway-400',
                                                  color:
                                                      getColor('suggested-contacts'),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Theme(
                              data: Theme.of(context)
                                  .copyWith(dividerColor: Colors.transparent),
                              child: ExpansionTile(
                                key: Key('contatti'),
                                controller: contattiTileKey,
                                initiallyExpanded: contattiTileExpand,
                                onExpansionChanged: ((value) => setState(
                                      () {
                                        contattiTileExpand = value;
                                      },
                                    )),
                                collapsedIconColor: Colors.white,
                                iconColor: Colors.white,
                                minTileHeight: 0,
                                leading: SizedBox(
                                  height: 30,
                                  width: 30,
                                  child: Center(
                                    child: SvgPicture.asset(
                                      "assets/icons/ic_contatti.svg",
                                      height: 21,
                                      width: 21,
                                      alignment: Alignment.center,
                                    ),
                                  ),
                                ),
                                childrenPadding:
                                    EdgeInsets.symmetric(horizontal: 15),
                                tilePadding: EdgeInsets.only(
                                    left: 15, right: 15, top: 5),
                                title: Text(
                                  'Contatti',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontFamily: 'semi-bold',
                                    color: Colors.white,
                                  ),
                                ),
                                trailing: SvgPicture.asset(
                                  contattiTileExpand
                                      ? 'assets/icons/arrow_up.svg'
                                      : 'assets/icons/arrow_down.svg',
                                  height: 6,
                                  width: 10,
                                  color: Colors.white,
                                ),
                                children: <Widget>[
                                  Padding(
                                    padding: const EdgeInsets.only(left: 42),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      children: [
                                        InkWell(
                                          onTap: () {
                                            widget.selectedView = 'web-lead';
                                            widget.updateViewCallback!(
                                                widget.selectedView);
                                          },
                                          child: Row(
                                            children: [
                                              Text(
                                                'Lead acquisto',
                                                style: TextStyle(
                                                  fontSize: 14,
                                                  fontFamily: 'Raleway-400',
                                                  color:
                                                      getColor('web-lead'),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        SizedBox(
                                          height: 8,
                                        ),
                                        InkWell(
                                          onTap: () {
                                            widget.selectedView = 'contatti-ricevuti';
                                            widget.updateViewCallback!(
                                                widget.selectedView);
                                          },
                                          child: Row(
                                            children: [
                                              Text(
                                                'Lead vendita',
                                                style: TextStyle(
                                                  fontSize: 14,
                                                  fontFamily: 'Raleway-400',
                                                  color: getColor('contatti-ricevuti'),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),

                          ],
                        ),
                      ),
                    ],
                  ),
          ),
        ),
        Positioned(
          right: -10,
          top: 42,
          child: InkWell(
            onTap: toggleDrawer,
            child: Container(
              height: 28,
              width: 28,
              child: Center(
                child: SvgPicture.asset(
                  isDrawerOpen
                      ? "assets/icons/arrow_left.svg"
                      : "assets/icons/arrow_right.svg",
                  color: AppColor.drawerIconButtonColor,
                  height: 10,
                ),
              ),
              decoration: BoxDecoration(
                boxShadow: [
                  BoxShadow(
                      offset: Offset(0, 10),
                      blurRadius: 10,
                      color: AppColor.black.withOpacity(0.1))
                ],
                color: AppColor.drawerButtonColor,
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        )
      ],
    );
  }

  Color? getColor(String view) {
    if (widget.selectedView == view) {
      return Theme.of(context).highlightColor; //Color(0xff489B79);
    } else {
      return Colors.white; //Color(0xff7D7D7D);
    }
  }
}

enum ReceivedContactsPageFilters {
  vendiNewarc,
  vendiAgenzie,
  valutaCompra,
  valutaCuriosita,
  vendiProfessionista,
  valutaProfessionista
}
